Performing hot restart...                                               
Restarted application in 3,516ms.
flutter: 🔥 Waiting for Firebase initialization...
flutter: 🚀 Starting app initialization
flutter: ✅ SharedPreferences initialized in 24ms
flutter: ✅ Firebase core initialized in 38ms
flutter: ✅ Preloaded asset: assets/images/splash.png (18355 bytes)
flutter: ✅ Preloaded asset: assets/animations/splash_animation.json (3741 bytes)
flutter: ✅ Critical assets preloaded in 37ms
flutter: ✅ App initialization completed in 52ms
flutter: ✅ Firebase initialization completed successfully
flutter: ✅ Preloaded asset: assets/images/onboarding_1.png (0 bytes)
flutter: ✅ Firebase full features initialized in 98ms
flutter: ✅ Preloaded asset: assets/images/onboarding_2.png (0 bytes)
flutter: ✅ Preloaded asset: assets/images/onboarding_3.png (0 bytes)
flutter: ✅ Non-critical assets preloaded in 108ms
flutter: ℹ️ INFO [2025-08-11T00:38:24.878753] [LoggingService] Logging service initialized successfully
flutter: ℹ️ INFO [2025-08-11T00:38:24.883899] [LoggingService] Device info: {name: iPhone 15 Pro Max, model: iPhone, systemName: iOS, systemVersion: 17.2, platform: ios}
flutter: ℹ️ INFO [2025-08-11T00:38:24.884777] [LoggingService] App info: Culture Connect 1.0.0+1
flutter: ℹ️ INFO [2025-08-11T00:38:24.900385] [ErrorHandlingService] Error handling service initialized
flutter: ℹ️ INFO [2025-08-11T00:38:24.920103] [CrashReportingService] Crash reporting service initialized
flutter: ℹ️ INFO [2025-08-11T00:38:24.934015] [AnalyticsService] Analytics service initialized
flutter: 🐛 DEBUG [2025-08-11T00:38:24.939463] [AnalyticsService] Event: app_session_begin {"category":"engagement","parameters":{"timestamp":"2025-08-11T00:38:24.927739"}}
flutter: ℹ️ INFO [2025-08-11T00:38:24.944165] [PerformanceMonitoringService] Performance monitoring service initialized
flutter: ℹ️ INFO [2025-08-11T00:38:24.944681] [App] All services initialized successfully
flutter: SplashVideoBackground: Video failed to load, using fallback
flutter: VideoBackground: Failed to load video - PlatformException(video_player, *** -[NSURL initFileURLWithPath:]: nil string parameter, null, null)
flutter: 🐛 DEBUG [2025-08-11T00:38:26.566810] [PerformanceMonitoringService] Slow frame detected {"duration_ms":156}
flutter: 🐛 DEBUG [2025-08-11T00:38:26.616444] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:26.666714] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:26.733404] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:26.800138] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:27.333405] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:27.883828] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:28.000082] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:28.133457] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:28.216735] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:28.366422] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:28.583079] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:32.783996] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-08-11T00:38:32.833409] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:32.866776] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:33.516349] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-08-11T00:38:33.550804] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:33.583420] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:33.616885] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:33.900201] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:34.367438] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:34.417414] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-08-11T00:38:34.466594] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-08-11T00:38:34.944982] [PerformanceMonitoringService] High memory usage detected {"memory_mb":194.0}
flutter: 🐛 DEBUG [2025-08-11T00:38:35.650080] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-08-11T00:38:35.716539] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-11T00:38:35.770797] [PerformanceMonitoringService] Slow frame detected {"duration_ms":37}
flutter: 🐛 DEBUG [2025-08-11T00:38:36.170717] [PerformanceMonitoringService] Slow frame detected {"duration_ms":378}
flutter: 🐛 DEBUG [2025-08-11T00:38:36.333535] [PerformanceMonitoringService] Slow frame detected {"duration_ms":183}
flutter: 🐛 DEBUG [2025-08-11T00:38:45.371648] [PerformanceMonitoringService] Slow frame detected {"duration_ms":254}
flutter: 🐛 DEBUG [2025-08-11T00:38:45.493272] [PerformanceMonitoringService] Slow frame detected {"duration_ms":120}
flutter: 🐛 DEBUG [2025-08-11T00:38:45.633468] [PerformanceMonitoringService] Slow frame detected {"duration_ms":141}
flutter: 🐛 DEBUG [2025-08-11T00:38:45.717048] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-08-11T00:38:45.785678] [PerformanceMonitoringService] Slow frame detected {"duration_ms":35}
flutter: 🐛 DEBUG [2025-08-11T00:38:45.983740] [PerformanceMonitoringService] Slow frame detected {"duration_ms":198}
flutter: 🐛 DEBUG [2025-08-11T00:38:46.333473] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:47.283682] [PerformanceMonitoringService] Slow frame detected {"duration_ms":366}
flutter: 🐛 DEBUG [2025-08-11T00:38:47.433550] [PerformanceMonitoringService] Slow frame detected {"duration_ms":150}
flutter: 🐛 DEBUG [2025-08-11T00:38:48.582099] [PerformanceMonitoringService] Slow frame detected {"duration_ms":81}
flutter: 🐛 DEBUG [2025-08-11T00:38:48.700945] [PerformanceMonitoringService] Slow frame detected {"duration_ms":118}
flutter: 🐛 DEBUG [2025-08-11T00:38:48.733383] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:48.866825] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: ⚠️ WARNING [2025-08-11T00:38:49.945249] [PerformanceMonitoringService] High memory usage detected {"memory_mb":195.0}
flutter: 🐛 DEBUG [2025-08-11T00:38:50.183364] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:51.233402] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:51.539924] [PerformanceMonitoringService] Slow frame detected {"duration_ms":56}
flutter: 🐛 DEBUG [2025-08-11T00:38:51.567568] [PerformanceMonitoringService] Slow frame detected {"duration_ms":26}
flutter: 🐛 DEBUG [2025-08-11T00:38:51.651041] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-08-11T00:38:51.916819] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:52.100259] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:52.300153] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:53.451080] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:53.634268] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:54.034028] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:54.217049] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:55.150210] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:55.933401] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:56.251055] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:56.467095] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:57.433632] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:57.634378] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:57.817015] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:58.058895] [PerformanceMonitoringService] Slow frame detected {"duration_ms":42}
flutter: 🐛 DEBUG [2025-08-11T00:38:58.083447] [PerformanceMonitoringService] Slow frame detected {"duration_ms":24}
flutter: 🐛 DEBUG [2025-08-11T00:38:58.167755] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:58.267381] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:38:59.400287] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:00.000173] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:01.616867] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: AuthService: Getting user model for UID: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: 🐛 DEBUG [2025-08-11T00:39:04.323824] [PerformanceMonitoringService] Slow frame detected {"duration_ms":57}
flutter: 🐛 DEBUG [2025-08-11T00:39:04.383903] [PerformanceMonitoringService] Slow frame detected {"duration_ms":59}
flutter: getCurrentUserModel attempt 1 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: ⚠️ WARNING [2025-08-11T00:39:04.944688] [PerformanceMonitoringService] High memory usage detected {"memory_mb":194.0}
flutter: Unexpected error during login: type 'List<Object?>' is not a subtype of type 'PigeonUserDetails?' in type cast
flutter: AutoLockService initialized with settings
flutter: Profile screen: Initializing profile...
flutter: Profile screen: Firebase Auth user found: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: Profile screen: _loadUserData called
flutter: currentUserModelProvider: [1754869145555] Starting to fetch user model...
flutter: AuthService: Getting user model for UID: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: Profile screen: Provider is loading, skipping fetch
flutter: Voice service initialized successfully for Kaia AI
flutter: 🐛 DEBUG [2025-08-11T00:39:06.070807] [PerformanceMonitoringService] Slow frame detected {"duration_ms":766}
flutter: 🐛 DEBUG [2025-08-11T00:39:06.193505] [EnhancedOfflineModeService] Loaded offline settings
flutter: 🐛 DEBUG [2025-08-11T00:39:06.197446] [EnhancedOfflineModeService] Loaded 0 offline content items
flutter: 🐛 DEBUG [2025-08-11T00:39:06.198997] [EnhancedOfflineModeService] Loaded 0 content conflicts
flutter: 🐛 DEBUG [2025-08-11T00:39:06.200858] [EnhancedOfflineModeService] Loaded 0 bandwidth usage records
flutter: 🐛 DEBUG [2025-08-11T00:39:06.206240] [EnhancedOfflineModeService] Loaded 1 sync schedules
flutter: getCurrentUserModel attempt 1 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-08-11T00:39:06.252011] [PerformanceMonitoringService] Slow frame detected {"duration_ms":166}
flutter: ❌ ERROR [2025-08-11T00:39:06.269138] [OfflineModeService] Failed to initialize PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-08-11T00:39:06.278145] [Error[OfflineModeService.initialize]] An unexpected error occurred. Please try again later. {"error":"PlatformException(UNAVAILABLE, Battery info unavailable, null, null)","type":"unknown","severity":"medium"}
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-08-11T00:39:06.283014] [PlatformError] PlatformException(UNAVAILABLE, Battery info unavailable, null, null) PlatformException(UNAVAILABLE, Battery info unavailable, null, null)
flutter: Stack trace:
flutter: #0      StandardMethodCodec.decodeEnvelope (package:flutter/src/services/message_codecs.dart:646:7)
flutter: #1      MethodChannel._invokeMethod (package:flutter/src/services/platform_channel.dart:334:18)
flutter: <asynchronous suspension>
flutter: #2      MethodChannelBattery.batteryLevel.<anonymous closure> (package:battery_plus_platform_interface/method_channel_battery_plus.dart:31:18)
flutter: <asynchronous suspension>
flutter: #3      OfflineModeService._initDeviceStateMonitoring (package:culture_connect/services/offline_mode_service.dart:237:21)
flutter: <asynchronous suspension>
flutter: #4      OfflineModeService.initialize (package:culture_connect/services/offline_mode_service.dart:160:7)
flutter: <asynchronous suspension>
flutter:
flutter: 🐛 DEBUG [2025-08-11T00:39:06.288388] [EnhancedOfflineModeService] Starting offline content sync
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-08-11T00:39:06.290323] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-08-11T00:39:06.302446] [OfflineModeService] Offline content sync completed
flutter: 🐛 DEBUG [2025-08-11T00:39:06.317449] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:06.383851] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:06.489370] [PerformanceMonitoringService] Slow frame detected {"duration_ms":72}
flutter: 🐛 DEBUG [2025-08-11T00:39:06.516856] [PerformanceMonitoringService] Slow frame detected {"duration_ms":27}
flutter: 🐛 DEBUG [2025-08-11T00:39:06.616952] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-08-11T00:39:06.700701] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:06.751221] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-08-11T00:39:06.869218] [PerformanceMonitoringService] Slow frame detected {"duration_ms":115}
flutter: 🐛 DEBUG [2025-08-11T00:39:06.904339] [PerformanceMonitoringService] Slow frame detected {"duration_ms":37}
flutter: 🐛 DEBUG [2025-08-11T00:39:06.938834] [PerformanceMonitoringService] Slow frame detected {"duration_ms":29}
flutter: 🐛 DEBUG [2025-08-11T00:39:06.971099] [PerformanceMonitoringService] Slow frame detected {"duration_ms":37}
flutter: 🐛 DEBUG [2025-08-11T00:39:07.005355] [PerformanceMonitoringService] Slow frame detected {"duration_ms":29}
flutter: 🐛 DEBUG [2025-08-11T00:39:07.039251] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:07.169260] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:07.350975] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: getCurrentUserModel attempt 2 failed: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.. Retrying in 1s...
flutter: 🐛 DEBUG [2025-08-11T00:39:07.430603] [PerformanceMonitoringService] Slow frame detected {"duration_ms":46}
flutter: 🐛 DEBUG [2025-08-11T00:39:07.467019] [PerformanceMonitoringService] Slow frame detected {"duration_ms":36}
flutter: 🐛 DEBUG [2025-08-11T00:39:07.516941] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:07.583610] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:07.634853] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: 🍽️ RestaurantListScreen: Loaded 15 restaurants
flutter: 🔍 RestaurantListScreen: Filtered to 15 restaurants
flutter: 🎴 RestaurantCard: Building card for The Fancy Bistro (gridMode: false)
flutter: 🎴 RestaurantCard: Building card for Sushi Paradise (gridMode: false)
flutter: 🎴 RestaurantCard: Building card for Spice Route (gridMode: false)
flutter: 🐛 DEBUG [2025-08-11T00:39:09.427169] [PerformanceMonitoringService] Slow frame detected {"duration_ms":302}
flutter: 🐛 DEBUG [2025-08-11T00:39:09.500210] [PerformanceMonitoringService] Slow frame detected {"duration_ms":80}
flutter: 🐛 DEBUG [2025-08-11T00:39:09.636756] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: ❌ ERROR [2025-08-11T00:39:09.665933] [FlutterError] HTTP request failed, statusCode: 404, https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80 HTTP request failed, statusCode: 404, https://images.unsplash.com/photo-1539650116574-75c0c6d73f6e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80
flutter: Stack trace:
flutter: #0      NetworkImage._loadAsync (package:flutter/src/painting/_network_image_io.dart:115:9)
flutter: <asynchronous suspension>
flutter: #1      MultiFrameImageStreamCompleter._handleCodecReady (package:flutter/src/painting/image_stream.dart:1013:3)
flutter: <asynchronous suspension>
flutter:
flutter: ❌ ERROR [2025-08-11T00:39:09.669048] [FlutterError] HTTP request failed, statusCode: 404, https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80 HTTP request failed, statusCode: 404, https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80
flutter: Stack trace:
flutter: #0      NetworkImage._loadAsync (package:flutter/src/painting/_network_image_io.dart:115:9)
flutter: <asynchronous suspension>
flutter: #1      MultiFrameImageStreamCompleter._handleCodecReady (package:flutter/src/painting/image_stream.dart:1013:3)
flutter: <asynchronous suspension>
flutter:
flutter: 🐛 DEBUG [2025-08-11T00:39:09.684648] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-11T00:39:09.834883] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: getCurrentUserModel failed after 3 attempts: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: currentUserModelProvider: AuthService call completed in 4739ms
flutter: currentUserModelProvider: AuthService returned null user model
flutter: currentUserModelProvider: Firebase Auth user: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1, verified: true
flutter: currentUserModelProvider: Firebase Auth user exists but no Firestore document. Creating...
flutter: _createMissingUserDocument: Starting document creation for KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: 🐛 DEBUG [2025-08-11T00:39:10.802289] [RestaurantAmenityChip] Building chip for amenity: Bar
flutter: 🐛 DEBUG [2025-08-11T00:39:10.805439] [RestaurantAmenityChip] Building chip for amenity: Vegetarian
flutter: 🐛 DEBUG [2025-08-11T00:39:10.808408] [RestaurantAmenityChip] Building chip for amenity: Gluten-Free
flutter: 🐛 DEBUG [2025-08-11T00:39:10.811255] [RestaurantAmenityChip] Building chip for amenity: WiFi
flutter: 🐛 DEBUG [2025-08-11T00:39:10.813863] [RestaurantAmenityChip] Building chip for amenity: Parking
flutter: 🐛 DEBUG [2025-08-11T00:39:10.816889] [RestaurantAmenityChip] Building chip for amenity: Accessible
flutter: ℹ️ INFO [2025-08-11T00:39:10.880291] [RestaurantDetailsScreen] Initializing mock data for restaurant: restaurant2
flutter: 🐛 DEBUG [2025-08-11T00:39:10.889785] [PerformanceMonitoringService] Slow frame detected {"duration_ms":256}
flutter: 🐛 DEBUG [2025-08-11T00:39:10.933670] [PerformanceMonitoringService] Slow frame detected {"duration_ms":43}
flutter: 🐛 DEBUG [2025-08-11T00:39:10.967465] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:11.001070] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:12.267791] [PerformanceMonitoringService] Slow frame detected {"duration_ms":100}
flutter: 🐛 DEBUG [2025-08-11T00:39:12.317169] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:12.867139] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-08-11T00:39:12.967334] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:13.000844] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:13.951051] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: _createMissingUserDocument: Error creating user document after 4502ms: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: _createMissingUserDocument: Stack trace: #0      FirebaseFirestoreHostApi.documentReferenceGet (package:cloud_firestore_platform_interface/src/pigeon/messages.pigeon.dart:1079:7)
flutter: <asynchronous suspension>
flutter: #1      MethodChannelDocumentReference.get (package:cloud_firestore_platform_interface/src/method_channel/method_channel_document_reference.dart:72:22)
flutter: <asynchronous suspension>
flutter: #2      _JsonDocumentReference.get (package:cloud_firestore/src/document_reference.dart:151:7)
flutter: <asynchronous suspension>
flutter: #3      _createMissingUserDocument (package:culture_connect/providers/auth_provider.dart:163:25)
flutter: <asynchronous suspension>
flutter: #4      Future.timeout.<anonymous closure> (dart:async/future_impl.dart:1004:15)
flutter: <asynchronous suspension>
flutter: #5      currentUserModelProvider.<anonymous closure> (package:culture_connect/providers/auth_provider.dart:102:11)
flutter: <asynchronous suspension>
flutter: #6      FutureHandlerProviderElementMixin.handleFuture.<anonymous closure>.<anonymous closure> (package:riverpod/src/async_notifier/base.dart:355:9)
flutter: <asynchronous suspension>
flutter:
flutter: currentUserModelProvider: Error during document creation: [cloud_firestore/unavailable] The service is currently unavailable. This is a most likely a transient condition and may be corrected by retrying with a backoff.
flutter: currentUserModelProvider: Returning null - no valid user model found
flutter: Profile screen user data: null
flutter: Profile screen: User model is null - creating fallback
flutter: Profile screen: Firebase Auth user: KjhT7Sx7N3dKPCtpiBYGS9oUV4y1
flutter: Profile screen: Firebase Auth email: <EMAIL>
flutter: Profile screen: Firebase Auth verified: true
flutter: Profile screen: Creating fallback user model from Firebase Auth
flutter: ⚠️ WARNING [2025-08-11T00:39:14.976787] [PerformanceMonitoringService] High memory usage detected {"memory_mb":176.0}
flutter: 🐛 DEBUG [2025-08-11T00:39:15.000706] [PerformanceMonitoringService] Slow frame detected {"duration_ms":200}
flutter: 🐛 DEBUG [2025-08-11T00:39:15.117140] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:15.484626] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-08-11T00:39:19.946454] [PerformanceMonitoringService] High memory usage detected {"memory_mb":196.0}
flutter: ℹ️ INFO [2025-08-11T00:39:21.896552] [RestaurantDetailsScreen] Navigating to reservation screen for restaurant: restaurant2
flutter: 🐛 DEBUG [2025-08-11T00:39:21.983604] [CalendarDayPicker] Initializing calendar day picker
flutter: 🐛 DEBUG [2025-08-11T00:39:21.985430] [CalendarDayPicker] Generating visible dates from 2025-08-11 00:39:21.924423 to 2025-09-10 00:39:21.924424
flutter: 🐛 DEBUG [2025-08-11T00:39:21.988022] [CalendarDayPicker] Generated 31 visible dates
flutter: 🐛 DEBUG [2025-08-11T00:39:21.988309] [CalendarDayPicker] Calendar day picker initialized with 31 dates
flutter: 🐛 DEBUG [2025-08-11T00:39:21.989569] [CalendarDayPicker] Building calendar day picker
flutter: 🐛 DEBUG [2025-08-11T00:39:22.040076] [PartySizeSelector] Building party size selector with current size: 2
flutter: ℹ️ INFO [2025-08-11T00:39:22.115230] [RestaurantReservationScreen] Initializing reservation screen for restaurant: restaurant2
flutter: 🐛 DEBUG [2025-08-11T00:39:22.119628] [CalendarDayPicker] Scrolling to selected date: 2025-08-11 00:39:21.921426
flutter: 🐛 DEBUG [2025-08-11T00:39:22.120534] [CalendarDayPicker] Selected date index: 0
flutter: 🐛 DEBUG [2025-08-11T00:39:22.121117] [CalendarDayPicker] Scrolling to offset: 0.0
flutter: 🐛 DEBUG [2025-08-11T00:39:22.151823] [PerformanceMonitoringService] Slow frame detected {"duration_ms":250}
flutter: 🐛 DEBUG [2025-08-11T00:39:22.164051] [CalendarDayPicker] Selected date changed, scrolling to new date: 2025-08-11 00:39:22.118433
flutter: 🐛 DEBUG [2025-08-11T00:39:22.164505] [CalendarDayPicker] Scrolling to selected date: 2025-08-11 00:39:22.118433
flutter: 🐛 DEBUG [2025-08-11T00:39:22.164876] [CalendarDayPicker] Selected date index: 0
flutter: 🐛 DEBUG [2025-08-11T00:39:22.165273] [CalendarDayPicker] Scrolling to offset: 0.0
flutter: 🐛 DEBUG [2025-08-11T00:39:22.165775] [CalendarDayPicker] Min/max dates changed, regenerating visible dates
flutter: 🐛 DEBUG [2025-08-11T00:39:22.166234] [CalendarDayPicker] Generating visible dates from 2025-08-11 00:39:22.155548 to 2025-09-10 00:39:22.155549
flutter: 🐛 DEBUG [2025-08-11T00:39:22.169464] [CalendarDayPicker] Generated 31 visible dates
flutter: 🐛 DEBUG [2025-08-11T00:39:22.169892] [CalendarDayPicker] Scrolling to selected date: 2025-08-11 00:39:22.118433
flutter: 🐛 DEBUG [2025-08-11T00:39:22.170405] [CalendarDayPicker] Selected date index: 0
flutter: 🐛 DEBUG [2025-08-11T00:39:22.170855] [CalendarDayPicker] Scrolling to offset: 0.0
flutter: 🐛 DEBUG [2025-08-11T00:39:22.171538] [CalendarDayPicker] Building calendar day picker
flutter: 🐛 DEBUG [2025-08-11T00:39:22.183634] [PartySizeSelector] Building party size selector with current size: 2
flutter: 🐛 DEBUG [2025-08-11T00:39:22.233965] [PerformanceMonitoringService] Slow frame detected {"duration_ms":83}
flutter: 🐛 DEBUG [2025-08-11T00:39:22.367399] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:22.692744] [TimeSlotSelector] Building time slot selector with 23 slots
flutter: 🐛 DEBUG [2025-08-11T00:39:22.693909] [TimeSlotSelector] Successfully sorted 23 time slots
flutter: 🐛 DEBUG [2025-08-11T00:39:22.694526] [TimeSlotSelector] Successfully grouped time slots into 12 hours
flutter: 🐛 DEBUG [2025-08-11T00:39:22.695003] [TimeSlotSelector] Time slots available for 12 hours
flutter: 🐛 DEBUG [2025-08-11T00:39:22.817717] [PerformanceMonitoringService] Slow frame detected {"duration_ms":133}
flutter: 🐛 DEBUG [2025-08-11T00:39:22.850823] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:24.375333] [CalendarDayPicker] Min/max dates changed, regenerating visible dates
flutter: 🐛 DEBUG [2025-08-11T00:39:24.375882] [CalendarDayPicker] Generating visible dates from 2025-08-11 00:39:24.370480 to 2025-09-10 00:39:24.370481
flutter: 🐛 DEBUG [2025-08-11T00:39:24.378397] [CalendarDayPicker] Generated 31 visible dates
flutter: 🐛 DEBUG [2025-08-11T00:39:24.378711] [CalendarDayPicker] Scrolling to selected date: 2025-08-11 00:39:22.118433
flutter: 🐛 DEBUG [2025-08-11T00:39:24.379070] [CalendarDayPicker] Selected date index: 0
flutter: 🐛 DEBUG [2025-08-11T00:39:24.379412] [CalendarDayPicker] Scrolling to offset: 0.0
flutter: 🐛 DEBUG [2025-08-11T00:39:24.379866] [CalendarDayPicker] Building calendar day picker
flutter: 🐛 DEBUG [2025-08-11T00:39:24.390069] [TimeSlotSelector] Building time slot selector with 23 slots
flutter: 🐛 DEBUG [2025-08-11T00:39:24.390894] [TimeSlotSelector] Successfully sorted 23 time slots
flutter: 🐛 DEBUG [2025-08-11T00:39:24.391307] [TimeSlotSelector] Successfully grouped time slots into 12 hours
flutter: 🐛 DEBUG [2025-08-11T00:39:24.391674] [TimeSlotSelector] Time slots available for 12 hours
flutter: 🐛 DEBUG [2025-08-11T00:39:24.434076] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-11T00:39:25.789423] [PartySizeSelector] Building party size selector with current size: 2
flutter: 🐛 DEBUG [2025-08-11T00:39:25.834509] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-08-11T00:39:25.901754] [PerformanceMonitoringService] Slow frame detected {"duration_ms":66}
flutter: 🐛 DEBUG [2025-08-11T00:39:26.184188] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ℹ️ INFO [2025-08-11T00:39:27.234657] [RestaurantReservationScreen] Handling continue button at step 0
flutter: ℹ️ INFO [2025-08-11T00:39:27.235445] [RestaurantReservationScreen] Advanced to step 1
flutter: 🐛 DEBUG [2025-08-11T00:39:27.247802] [CalendarDayPicker] Min/max dates changed, regenerating visible dates
flutter: 🐛 DEBUG [2025-08-11T00:39:27.248304] [CalendarDayPicker] Generating visible dates from 2025-08-11 00:39:27.236289 to 2025-09-10 00:39:27.236290
flutter: 🐛 DEBUG [2025-08-11T00:39:27.251267] [CalendarDayPicker] Generated 31 visible dates
flutter: 🐛 DEBUG [2025-08-11T00:39:27.251590] [CalendarDayPicker] Scrolling to selected date: 2025-08-11 00:39:22.118433
flutter: 🐛 DEBUG [2025-08-11T00:39:27.252050] [CalendarDayPicker] Selected date index: 0
flutter: 🐛 DEBUG [2025-08-11T00:39:27.252372] [CalendarDayPicker] Scrolling to offset: 0.0
flutter: 🐛 DEBUG [2025-08-11T00:39:27.252935] [CalendarDayPicker] Building calendar day picker
flutter: 🐛 DEBUG [2025-08-11T00:39:27.260977] [TimeSlotSelector] Building time slot selector with 23 slots
flutter: 🐛 DEBUG [2025-08-11T00:39:27.261313] [TimeSlotSelector] Successfully sorted 23 time slots
flutter: 🐛 DEBUG [2025-08-11T00:39:27.261609] [TimeSlotSelector] Successfully grouped time slots into 12 hours
flutter: 🐛 DEBUG [2025-08-11T00:39:27.262012] [TimeSlotSelector] Time slots available for 12 hours
flutter: 🐛 DEBUG [2025-08-11T00:39:27.288358] [PartySizeSelector] Building party size selector with current size: 2
flutter: 🐛 DEBUG [2025-08-11T00:39:27.351215] [PerformanceMonitoringService] Slow frame detected {"duration_ms":116}
flutter: 🐛 DEBUG [2025-08-11T00:39:27.417655] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: ℹ️ INFO [2025-08-11T00:39:31.322831] [RestaurantReservationScreen] Handling continue button at step 1
flutter: ℹ️ INFO [2025-08-11T00:39:31.323345] [RestaurantReservationScreen] Advanced to step 2
flutter: 🐛 DEBUG [2025-08-11T00:39:31.340377] [CalendarDayPicker] Min/max dates changed, regenerating visible dates
flutter: 🐛 DEBUG [2025-08-11T00:39:31.340990] [CalendarDayPicker] Generating visible dates from 2025-08-11 00:39:31.334470 to 2025-09-10 00:39:31.334471
flutter: 🐛 DEBUG [2025-08-11T00:39:31.343531] [CalendarDayPicker] Generated 31 visible dates
flutter: 🐛 DEBUG [2025-08-11T00:39:31.343898] [CalendarDayPicker] Scrolling to selected date: 2025-08-11 00:39:22.118433
flutter: 🐛 DEBUG [2025-08-11T00:39:31.344324] [CalendarDayPicker] Selected date index: 0
flutter: 🐛 DEBUG [2025-08-11T00:39:31.344692] [CalendarDayPicker] Scrolling to offset: 0.0
flutter: 🐛 DEBUG [2025-08-11T00:39:31.345339] [CalendarDayPicker] Building calendar day picker
flutter: 🐛 DEBUG [2025-08-11T00:39:31.351468] [TimeSlotSelector] Building time slot selector with 23 slots
flutter: 🐛 DEBUG [2025-08-11T00:39:31.351827] [TimeSlotSelector] Successfully sorted 23 time slots
flutter: 🐛 DEBUG [2025-08-11T00:39:31.352154] [TimeSlotSelector] Successfully grouped time slots into 12 hours
flutter: 🐛 DEBUG [2025-08-11T00:39:31.352524] [TimeSlotSelector] Time slots available for 12 hours
flutter: 🐛 DEBUG [2025-08-11T00:39:31.374717] [PartySizeSelector] Building party size selector with current size: 2
flutter: 🐛 DEBUG [2025-08-11T00:39:31.418175] [PerformanceMonitoringService] Slow frame detected {"duration_ms":84}
flutter: 🐛 DEBUG [2025-08-11T00:39:31.467291] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:31.500780] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:31.547953] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:31.567879] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:33.901508] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-08-11T00:39:34.946569] [PerformanceMonitoringService] High memory usage detected {"memory_mb":196.0}
flutter: 🐛 DEBUG [2025-08-11T00:39:42.450843] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:42.551147] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:42.617501] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:42.667666] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:44.450846] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:44.635194] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:44.801599] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:45.084316] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:45.235300] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:45.484969] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:46.101128] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:46.151942] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:46.318034] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:46.501180] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:46.634662] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:46.734494] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:48.168606] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-08-11T00:39:49.946721] [PerformanceMonitoringService] High memory usage detected {"memory_mb":196.0}
flutter: 🐛 DEBUG [2025-08-11T00:39:51.101967] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:51.268613] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:51.684376] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:51.801467] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:51.934853] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:52.301096] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:52.484340] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:52.635481] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:53.268025] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:53.451155] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:53.634527] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:53.801326] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:54.634725] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:54.901160] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:56.167896] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:56.567789] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:56.968040] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:57.367937] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:57.484654] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:57.584877] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:57.684825] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:57.784608] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:57.885065] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:39:58.601082] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:40:00.084696] [PerformanceMonitoringService] Slow frame detected {"duration_ms":50}
flutter: 🐛 DEBUG [2025-08-11T00:40:03.185543] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:40:03.284814] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-08-11T00:40:04.947348] [PerformanceMonitoringService] High memory usage detected {"memory_mb":197.0}
flutter: 🐛 DEBUG [2025-08-11T00:40:05.602576] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:40:06.267913] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:40:06.568093] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:40:08.401601] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:40:09.017936] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:40:09.251783] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:40:09.785315] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:40:10.751376] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:40:10.868009] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:40:14.869128] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ℹ️ INFO [2025-08-11T00:40:17.108352] [RestaurantReservationScreen] Handling continue button at step 2
flutter: ℹ️ INFO [2025-08-11T00:40:17.110833] [RestaurantReservationScreen] Submitting reservation for restaurant: restaurant2
flutter: ⚠️ WARNING [2025-08-11T00:40:17.111582] [RestaurantReservationScreen] Missing required information for reservation: user not logged in
flutter: ❌ ERROR [2025-08-11T00:40:17.139158] [FlutterError] Bad state: Cannot use "ref" after the widget was disposed. Bad state: Cannot use "ref" after the widget was disposed.
flutter: Stack trace:
flutter: #0      ConsumerStatefulElement._assertNotDisposed (package:flutter_riverpod/src/consumer.dart:550:7)
flutter: #1      ConsumerStatefulElement.read (package:flutter_riverpod/src/consumer.dart:619:5)
flutter: #2      _CalendarDayPickerState.dispose (package:culture_connect/widgets/travel/restaurant/calendar_day_picker.dart:107:24)
flutter: #3      StatefulElement.unmount (package:flutter/src/widgets/framework.dart:5840:11)
flutter: #4      ConsumerStatefulElement.unmount (package:flutter_riverpod/src/consumer.dart:575:11)
flutter: #5      _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2078:13)
flutter: #6      _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #7      MultiChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:7012:16)
flutter: #8      _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #9      _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #10     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6900:14)
flutter: #11     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #12     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #13     MultiChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:7012:16)
flutter: #14     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #15     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #16     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6900:14)
flutter: #17     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #18     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #19     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6900:14)
flutter: #20     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #21     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #22     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #23     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #24     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #25     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #26     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #27     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #28     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #29     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #30     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #31     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6900:14)
flutter: #32     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #33     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #34     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6900:14)
flutter: #35     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #36     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #37     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #38     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #39     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #40     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #41     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #42     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #43     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #44     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #45     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #46     MultiChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:7012:16)
flutter: #47     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #48     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #49     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6900:14)
flutter: #50     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #51     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #52     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #53     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #54     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #55     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6900:14)
flutter: #56     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #57     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #58     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #59     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #60     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #61     MultiChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:7012:16)
flutter: #62     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #63     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #64     MultiChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:7012:16)
flutter: #65     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #66     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #67     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6900:14)
flutter: #68     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #69     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #70     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6900:14)
flutter: #71     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #72     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #73     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #74     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #75     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #76     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #77     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #78     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #79     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #80     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #81     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #82     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #83     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #84     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #85     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #86     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #87     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #88     List.forEach (dart:core-patch/growable_array.dart:417:8)
flutter: #89     SliverMultiBoxAdaptorElement.visitChildren (package:flutter/src/widgets/sliver.dart:1173:52)
flutter: #90     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #91     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #92     ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #93     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #94     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #95     SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6900:14)
flutter: #96     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #97     _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #98     MultiChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:7012:16)
flutter: #99     _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #100    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #101    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6900:14)
flutter: #102    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #103    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #104    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6900:14)
flutter: #105    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #106    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #107    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6900:14)
flutter: #108    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #109    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #110    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6900:14)
flutter: #111    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #112    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #113    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #114    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #115    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #116    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6900:14)
flutter: #117    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #118    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #119    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #120    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #121    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #122    SingleChildRenderObjectElement.visitChildren (package:flutter/src/widgets/framework.dart:6900:14)
flutter: #123    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #124    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #125    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #126    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #127    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #128    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #129    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #130    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #131    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #132    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #133    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #134    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #135    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #136    _InactiveElements._unmount.<anonymous closure> (package:flutter/src/widgets/framework.dart:2076:7)
flutter: #137    ComponentElement.visitChildren (package:flutter/src/widgets/framework.dart:5683:14)
flutter: #138    _InactiveElements._unmount (package:flutter/src/widgets/framework.dart:2074:13)
flutter: #139    ListIterable.forEach (dart:_internal/iterable.dart:49:13)
flutter: #140    _InactiveElements._unmountAll (package:flutter/src/widgets/framework.dart:2087:25)
flutter: #141    BuildOwner.lockState (package:flutter/src/widgets/framework.dart:2954:15)
flutter: #142    BuildOwner.finalizeTree (package:flutter/src/widgets/framework.dart:3264:7)
flutter: #143    WidgetsBinding.drawFrame (package:flutter/src/widgets/binding.dart:1183:19)
flutter: #144    RendererBinding._handlePersistentFrameCallback (package:flutter/src/rendering/binding.dart:475:5)
flutter: #145    SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #146    SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1318:9)
flutter: #147    SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #148    _invoke (dart:ui/hooks.dart:312:13)
flutter: #149    PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #150    _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-08-11T00:40:17.191695] [FlutterError] Looking up a deactivated widget's ancestor is unsafe.
flutter: At this point the state of the widget's element tree is no longer stable.
flutter: To safely refer to a widget's ancestor in its dispose() method, save a reference to the ancestor by calling dependOnInheritedWidgetOfExactType() in the widget's didChangeDependencies() method. Looking up a deactivated widget's ancestor is unsafe.
flutter: At this point the state of the widget's element tree is no longer stable.
flutter: To safely refer to a widget's ancestor in its dispose() method, save a reference to the ancestor by calling dependOnInheritedWidgetOfExactType() in the widget's didChangeDependencies() method.
flutter: Stack trace:
flutter: #0      Element._debugCheckStateIsActiveForAncestorLookup.<anonymous closure> (package:flutter/src/widgets/framework.dart:4885:9)
flutter: #1      Element._debugCheckStateIsActiveForAncestorLookup (package:flutter/src/widgets/framework.dart:4899:6)
flutter: #2      Element.getElementForInheritedWidgetOfExactType (package:flutter/src/widgets/framework.dart:4935:12)
flutter: #3      InheritedModel._findModels (package:flutter/src/widgets/inherited_model.dart:148:45)
flutter: #4      InheritedModel.inheritFrom (package:flutter/src/widgets/inherited_model.dart:196:5)
flutter: #5      MediaQuery._maybeOf (package:flutter/src/widgets/media_query.dart:1246:27)
flutter: #6      MediaQuery.maybeTextScalerOf (package:flutter/src/widgets/media_query.dart:1366:65)
flutter: #7      MediaQuery.textScalerOf (package:flutter/src/widgets/media_query.dart:1356:59)
flutter: #8      EditableTextState._updateSelectionRects (package:flutter/src/widgets/editable_text.dart:4445:58)
flutter: #9      EditableTextState._schedulePeriodicPostFrameCallbacks (package:flutter/src/widgets/editable_text.dart:4421:5)
flutter: #10     SchedulerBinding._invokeFrameCallback (package:flutter/src/scheduler/binding.dart:1397:15)
flutter: #11     SchedulerBinding.handleDrawFrame (package:flutter/src/scheduler/binding.dart:1331:11)
flutter: #12     SchedulerBinding._handleDrawFrame (package:flutter/src/scheduler/binding.dart:1176:5)
flutter: #13     _invoke (dart:ui/hooks.dart:312:13)
flutter: #14     PlatformDispatcher._drawFrame (dart:ui/platform_dispatcher.dart:427:5)
flutter: #15     _drawFrame (dart:ui/hooks.dart:283:31)
flutter:
flutter: ❌ ERROR [2025-08-11T00:40:17.193927] [PlatformError] Looking up a deactivated widget's ancestor is unsafe.
flutter: At this point the state of the widget's element tree is no longer stable.
flutter: To safely refer to a widget's ancestor in its dispose() method, save a reference to the ancestor by calling dependOnInheritedWidgetOfExactType() in the widget's didChangeDependencies() method. Looking up a deactivated widget's ancestor is unsafe.
flutter: At this point the state of the widget's element tree is no longer stable.
flutter: To safely refer to a widget's ancestor in its dispose() method, save a reference to the ancestor by calling dependOnInheritedWidgetOfExactType() in the widget's didChangeDependencies() method.
flutter: Stack trace:
flutter: #0      Element._debugCheckStateIsActiveForAncestorLookup.<anonymous closure> (package:flutter/src/widgets/framework.dart:4885:9)
flutter: #1      Element._debugCheckStateIsActiveForAncestorLookup (package:flutter/src/widgets/framework.dart:4899:6)
flutter: #2      Element.dependOnInheritedWidgetOfExactType (package:flutter/src/widgets/framework.dart:4919:12)
flutter: #3      LookupBoundary.dependOnInheritedWidgetOfExactType (package:flutter/src/widgets/lookup_boundary.dart:96:13)
flutter: #4      View.maybeOf (package:flutter/src/widgets/view.dart:130:27)
flutter: #5      View.of (package:flutter/src/widgets/view.dart:151:33)
flutter: #6      EditableTextState.didChangeMetrics (package:flutter/src/widgets/editable_text.dart:4109:38)
flutter: #7      WidgetsBinding.handleMetricsChanged (package:flutter/src/widgets/binding.dart:755:16)
flutter: #8      _invoke (dart:ui/hooks.dart:312:13)
flutter: #9      PlatformDispatcher._updateWindowMetrics (dart:ui/platform_dispatcher.dart:316:5)
flutter: #10     _updateWindowMetrics (dart:ui/hooks.dart:222:31)
flutter:
flutter: ❌ ERROR [2025-08-11T00:40:17.194418] [PlatformError] Looking up a deactivated widget's ancestor is unsafe.
flutter: At this point the state of the widget's element tree is no longer stable.
flutter: To safely refer to a widget's ancestor in its dispose() method, save a reference to the ancestor by calling dependOnInheritedWidgetOfExactType() in the widget's didChangeDependencies() method. Looking up a deactivated widget's ancestor is unsafe.
flutter: At this point the state of the widget's element tree is no longer stable.
flutter: To safely refer to a widget's ancestor in its dispose() method, save a reference to the ancestor by calling dependOnInheritedWidgetOfExactType() in the widget's didChangeDependencies() method.
flutter: Stack trace:
flutter: #0      Element._debugCheckStateIsActiveForAncestorLookup.<anonymous closure> (package:flutter/src/widgets/framework.dart:4885:9)
flutter: #1      Element._debugCheckStateIsActiveForAncestorLookup (package:flutter/src/widgets/framework.dart:4899:6)
flutter: #2      Element.dependOnInheritedWidgetOfExactType (package:flutter/src/widgets/framework.dart:4919:12)
flutter: #3      LookupBoundary.dependOnInheritedWidgetOfExactType (package:flutter/src/widgets/lookup_boundary.dart:96:13)
flutter: #4      View.maybeOf (package:flutter/src/widgets/view.dart:130:27)
flutter: #5      View.of (package:flutter/src/widgets/view.dart:151:33)
flutter: #6      EditableTextState.didChangeMetrics (package:flutter/src/widgets/editable_text.dart:4109:38)
flutter: #7      WidgetsBinding.handleMetricsChanged (package:flutter/src/widgets/binding.dart:755:16)
flutter: #8      _invoke (dart:ui/hooks.dart:312:13)
flutter: #9      PlatformDispatcher._updateWindowMetrics (dart:ui/platform_dispatcher.dart:316:5)
flutter: #10     _updateWindowMetrics (dart:ui/hooks.dart:222:31)
flutter:
flutter: 🐛 DEBUG [2025-08-11T00:40:17.218493] [PerformanceMonitoringService] Slow frame detected {"duration_ms":99}
flutter: 🐛 DEBUG [2025-08-11T00:40:19.184808] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: 🐛 DEBUG [2025-08-11T00:40:19.551799] [PerformanceMonitoringService] Slow frame detected {"duration_ms":33}
flutter: ⚠️ WARNING [2025-08-11T00:40:19.947607] [PerformanceMonitoringService] High memory usage detected {"memory_mb":197.0}
flutter: 🐛 DEBUG [2025-08-11T00:40:21.504068] [PerformanceMonitoringService] Slow frame detected {"duration_ms":35}
flutter: ⚠️ WARNING [2025-08-11T00:40:34.947622] [PerformanceMonitoringService] High memory usage detected {"memory_mb":197.0}
