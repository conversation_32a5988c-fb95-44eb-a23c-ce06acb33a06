import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/travel/restaurant/policy_badge.dart';
import 'package:culture_connect/models/travel/restaurant_tier.dart';

/// Restaurant card widget using Trending Experiences card design as template
class RestaurantCard extends StatefulWidget {
  final Restaurant restaurant;
  final VoidCallback? onTap;
  final bool isGridMode;

  const RestaurantCard({
    super.key,
    required this.restaurant,
    this.onTap,
    this.isGridMode = false,
  });

  @override
  State<RestaurantCard> createState() => _RestaurantCardState();
}

class _RestaurantCardState extends State<RestaurantCard>
    with TickerProviderStateMixin {
  late AnimationController _hoverController;
  late AnimationController _favoriteController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _favoriteAnimation;

  bool _isFavorite = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _hoverController = AnimationController(
      duration: AppTheme.animationNormal,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.02,
    ).animate(CurvedAnimation(
      parent: _hoverController,
      curve: AppTheme.curveEmphasized,
    ));

    _favoriteController = AnimationController(
      duration: AppTheme.animationFast,
      vsync: this,
    );
    _favoriteAnimation = Tween<double>(
      begin: 1.0,
      end: 1.3,
    ).animate(CurvedAnimation(
      parent: _favoriteController,
      curve: Curves.elasticOut,
    ));
  }

  @override
  void dispose() {
    _hoverController.dispose();
    _favoriteController.dispose();
    super.dispose();
  }

  void _handleHoverEnter(PointerEnterEvent event) {
    _hoverController.forward();
  }

  void _handleHoverExit(PointerExitEvent event) {
    _hoverController.reverse();
  }

  void _toggleFavorite() {
    setState(() {
      _isFavorite = !_isFavorite;
    });
    _favoriteController.forward().then((_) {
      _favoriteController.reverse();
    });
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: _handleHoverEnter,
      onExit: _handleHoverExit,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              margin: const EdgeInsets.only(bottom: AppTheme.spacingMedium),
              // Add height constraint for grid mode to prevent overflow
              // Reduced to account for bottom margin (16px) being outside constraint
              constraints: widget.isGridMode
                  ? const BoxConstraints(maxHeight: 264)
                  : null,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusLarge),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(12),
                    blurRadius: 16,
                    offset: const Offset(0, 6),
                    spreadRadius: 0,
                  ),
                  BoxShadow(
                    color: Colors.black.withAlpha(6),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                    spreadRadius: 0,
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: widget.onTap,
                  borderRadius: BorderRadius.circular(16),
                  child: ClipRect(
                    // Safety net to prevent visual overflow in grid mode
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Restaurant Image Section
                        _buildImageSection(),
                        // Restaurant Content Section
                        _buildContentSection(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildImageSection() {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
          child: Stack(
            children: [
              CachedNetworkImage(
                imageUrl: widget.restaurant.imageUrl,
                width: double.infinity,
                height: widget.isGridMode ? 140 : 200,
                fit: BoxFit.cover,
                fadeInDuration: AppTheme.animationFast,
                // Performance optimizations
                memCacheWidth: 400,
                memCacheHeight: 300,
                maxWidthDiskCache: 800,
                maxHeightDiskCache: 600,
                placeholder: (context, url) => Container(
                  width: double.infinity,
                  height: widget.isGridMode ? 140 : 200,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppTheme.surfaceVariant,
                        AppTheme.surfaceVariant.withAlpha(128),
                      ],
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.restaurant_outlined,
                        color: AppTheme.textSecondaryColor.withAlpha(128),
                        size: 32,
                      ),
                      const SizedBox(height: AppTheme.spacingSmall),
                      Text(
                        'Loading...',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.textSecondaryColor.withAlpha(128),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
                errorWidget: (context, url, error) => Container(
                  width: double.infinity,
                  height: widget.isGridMode ? 140 : 200,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        AppTheme.errorColor.withAlpha(26),
                        AppTheme.errorColor.withAlpha(13),
                      ],
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.broken_image_outlined,
                        color: AppTheme.errorColor.withAlpha(128),
                        size: 32,
                      ),
                      const SizedBox(height: AppTheme.spacingSmall),
                      Text(
                        'Image unavailable',
                        style: TextStyle(
                          fontSize: 12,
                          color: AppTheme.errorColor.withAlpha(128),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              // Gradient overlay for better text readability and premium feel
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(AppTheme.borderRadiusLarge),
                      topRight: Radius.circular(AppTheme.borderRadiusLarge),
                    ),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withAlpha(77), // 30% opacity
                        Colors.black.withAlpha(153), // 60% opacity
                      ],
                      stops: const [0.0, 0.5, 1.0],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        // Restaurant Type Badge
        Positioned(
          top: AppTheme.spacingMedium,
          left: AppTheme.spacingMedium,
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: AppTheme.spacingSmall + 2,
              vertical: AppTheme.spacingXs + 2,
            ),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.black.withAlpha(204), // 80% opacity
                  Colors.black.withAlpha(179), // 70% opacity
                ],
              ),
              borderRadius:
                  BorderRadius.circular(AppTheme.borderRadiusSmall + 2),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(51), // 20% opacity
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Text(
              widget.restaurant.restaurantType.displayName,
              style: const TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w700,
                color: Colors.white,
                letterSpacing: 0.5,
              ),
            ),
          ),
        ),
        // Sale Badge (if on sale) - Positioned at bottom of image area in grid mode
        if (widget.restaurant.isOnSale)
          Positioned(
            bottom: widget.isGridMode
                ? AppTheme.spacingSmall
                : AppTheme.spacingMedium,
            right: AppTheme.spacingMedium,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: AppTheme.spacingSmall + 2,
                vertical: AppTheme.spacingXs + 1,
              ),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    AppTheme.errorColor,
                    AppTheme.errorColor.withAlpha(230), // 90% opacity
                  ],
                ),
                borderRadius:
                    BorderRadius.circular(AppTheme.borderRadiusSmall + 2),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.errorColor.withAlpha(77), // 30% opacity
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Text(
                '${widget.restaurant.discountPercentage}% OFF',
                style: const TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.w800,
                  color: Colors.white,
                  letterSpacing: 0.5,
                ),
              ),
            ),
          ),
        // Favorite Button
        Positioned(
          top: AppTheme.spacingMedium,
          right: AppTheme.spacingMedium,
          child: AnimatedBuilder(
            animation: _favoriteAnimation,
            builder: (context, child) {
              return Transform.scale(
                scale: _favoriteAnimation.value,
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    borderRadius: BorderRadius.circular(12),
                    onTap: _toggleFavorite,
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            Colors.white.withAlpha(242), // 95% opacity
                            Colors.white.withAlpha(230), // 90% opacity
                          ],
                        ),
                        borderRadius: BorderRadius.circular(
                            AppTheme.borderRadiusSmall + 4),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(26), // 10% opacity
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        _isFavorite ? Icons.favorite : Icons.favorite_border,
                        size: 18,
                        color: _isFavorite
                            ? AppTheme.errorColor
                            : AppTheme.textSecondaryColor,
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildContentSection() {
    return Flexible(
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            // Restaurant Name with Tier Badge
            Row(
              children: [
                Expanded(
                  child: Text(
                    widget.restaurant.name,
                    style: TextStyle(
                      fontSize: widget.isGridMode ? 16 : 18,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimaryColor,
                    ),
                    maxLines: widget.isGridMode ? 1 : 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 8),
                _buildTierBadge(),
              ],
            ),
            SizedBox(height: widget.isGridMode ? 6 : AppTheme.spacingSmall),

            // Cuisine Types
            Text(
              widget.restaurant.formattedCuisineTypes,
              style: TextStyle(
                fontSize: widget.isGridMode ? 12 : 14,
                color: AppTheme.textSecondaryColor,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: widget.isGridMode ? 6 : AppTheme.spacingSmall),

            // Location
            Row(
              children: [
                Icon(
                  Icons.location_on_outlined,
                  size: widget.isGridMode ? 14 : 16,
                  color: AppTheme.textSecondaryColor,
                ),
                const SizedBox(width: AppTheme.spacingXs),
                Expanded(
                  child: Text(
                    _getOptimizedLocation(),
                    style: TextStyle(
                      fontSize: widget.isGridMode ? 12 : 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
            SizedBox(height: widget.isGridMode ? 6 : AppTheme.spacingSmall),

            // Policy Badges
            _buildPolicyBadges(),
            // Conditional spacing - only add space if policy badges are shown (list mode)
            if (!widget.isGridMode)
              const SizedBox(height: AppTheme.spacingSmall),

            // Rating and Price Row
            Row(
              children: [
                // Rating section with fixed width to prevent truncation
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.star_rounded,
                      size: widget.isGridMode ? 14 : 16,
                      color: const Color(0xFFFFB800),
                    ),
                    const SizedBox(width: AppTheme.spacingXs),
                    Text(
                      widget.restaurant.rating.toStringAsFixed(1),
                      style: TextStyle(
                        fontSize: widget.isGridMode ? 12 : 14,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimaryColor,
                      ),
                    ),
                    const SizedBox(width: AppTheme.spacingXs),
                    Text(
                      '(${widget.restaurant.reviewCount})',
                      style: TextStyle(
                        fontSize: widget.isGridMode ? 10 : 13,
                        color: AppTheme.textSecondaryColor,
                      ),
                    ),
                  ],
                ),
                const Spacer(),

                // Price
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: widget.isGridMode ? 6 : AppTheme.spacingSmall,
                    vertical: widget.isGridMode ? 4 : AppTheme.spacingXs,
                  ),
                  decoration: BoxDecoration(
                    gradient: AppTheme.accentGradient,
                    borderRadius:
                        BorderRadius.circular(AppTheme.borderRadiusSmall),
                  ),
                  child: Text(
                    widget.restaurant.formattedPrice,
                    style: TextStyle(
                      fontSize: widget.isGridMode ? 12 : 14,
                      fontWeight: FontWeight.w700,
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build policy badges for deposit and cancellation information
  Widget _buildPolicyBadges() {
    // Hide policy badges in grid mode to reduce visual clutter
    if (widget.isGridMode) {
      return const SizedBox.shrink();
    }

    final badges = PolicyBadgeUtils.createRestaurantBadges(
      widget.restaurant.tier,
      isCompact: false, // Always false since we're only in list mode here
      includeTier: false, // Don't show tier badge to keep it clean
    );

    return PolicyBadgeRow(
      badges: badges,
      spacing: 8.0,
      wrap: true,
    );
  }

  /// Build tier identification badge (T-1, T-2, T-3)
  Widget _buildTierBadge() {
    final tier = widget.restaurant.tier;
    final tierLabel = _getTierLabel(tier);
    final tierColor = tier.color;

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: widget.isGridMode ? 6.0 : 8.0,
        vertical: widget.isGridMode ? 3.0 : 4.0,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            tierColor,
            tierColor.withAlpha(204), // 80% opacity for gradient effect
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: tierColor.withAlpha(77), // 30% opacity
            blurRadius: 4,
            offset: const Offset(0, 2),
            spreadRadius: 0,
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.workspace_premium,
            size: widget.isGridMode ? 12.0 : 14.0,
            color: Colors.white,
          ),
          const SizedBox(width: 3),
          Text(
            tierLabel,
            style: TextStyle(
              fontSize: widget.isGridMode ? 10.0 : 11.0,
              fontWeight: FontWeight.w700,
              color: Colors.white,
              letterSpacing: 0.5,
            ),
          ),
        ],
      ),
    );
  }

  /// Get tier label (T-1, T-2, T-3)
  String _getTierLabel(RestaurantTier tier) {
    switch (tier) {
      case RestaurantTier.highEnd:
        return 'T-1';
      case RestaurantTier.middleClass:
        return 'T-2';
      case RestaurantTier.lowEnd:
        return 'T-3';
    }
  }

  /// Get optimized location text for grid view
  String _getOptimizedLocation() {
    if (!widget.isGridMode) {
      return widget.restaurant.location;
    }

    // For grid mode, extract the most relevant part of the location
    final location = widget.restaurant.location;

    // Split by comma and prioritize city/area over street address
    final parts = location.split(',').map((part) => part.trim()).toList();

    if (parts.length >= 2) {
      // If we have multiple parts, prefer the last two (usually city, country)
      // or the most descriptive parts
      if (parts.length >= 3) {
        // Take the last two parts (usually city, country)
        return '${parts[parts.length - 2]}, ${parts[parts.length - 1]}';
      } else {
        // Take the last part (usually city or area)
        return parts.last;
      }
    }

    // If no comma separation, return the original but truncated if too long
    if (location.length > 20) {
      return '${location.substring(0, 17)}...';
    }

    return location;
  }
}
