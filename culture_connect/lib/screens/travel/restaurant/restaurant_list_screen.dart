import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/travel/restaurant_provider.dart'
    as restaurant_provider;
import 'package:culture_connect/models/travel/restaurant_tier.dart';
import 'package:culture_connect/screens/travel/restaurant/restaurant_details_screen_enhanced.dart';
import 'package:culture_connect/widgets/travel/restaurant/restaurant_card.dart';

/// Screen for displaying a list of restaurants
class RestaurantListScreen extends ConsumerStatefulWidget {
  /// Creates a new restaurant list screen
  const RestaurantListScreen({super.key});

  @override
  ConsumerState<RestaurantListScreen> createState() =>
      _RestaurantListScreenState();
}

class _RestaurantListScreenState extends ConsumerState<RestaurantListScreen>
    with SingleTickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  bool _isSearching = false;
  bool _isGridView = false;
  bool _isFilterVisible = false;
  final TextEditingController _searchController = TextEditingController();

  // Filter state
  CuisineType? _selectedCuisineType;
  RestaurantTier? _selectedTier;
  double _minPrice = 0;
  double _maxPrice = 500;
  double _minRating = 0;

  // Animation controller for filter panel
  late AnimationController _filterAnimationController;
  late Animation<double> _filterAnimation;

  @override
  void initState() {
    super.initState();
    _filterAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 300),
    );
    _filterAnimation = CurvedAnimation(
      parent: _filterAnimationController,
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _searchController.dispose();
    _filterAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: _isSearching
            ? TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: 'Search restaurants...',
                  border: InputBorder.none,
                  hintStyle: TextStyle(
                      color: theme.colorScheme.onSurface.withAlpha(153)),
                ),
                style: TextStyle(color: theme.colorScheme.onSurface),
                autofocus: true,
                onChanged: (value) {
                  // Implement search functionality
                  setState(() {});
                },
              )
            : const Text('Restaurants'),
        actions: [
          // Toggle grid/list view
          IconButton(
            icon: Icon(_isGridView ? Icons.view_list : Icons.grid_view),
            tooltip: _isGridView ? 'List View' : 'Grid View',
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
          ),
          // Filter button
          IconButton(
            icon: const Icon(Icons.filter_list),
            tooltip: 'Filter',
            onPressed: () {
              setState(() {
                _isFilterVisible = !_isFilterVisible;
                if (_isFilterVisible) {
                  _filterAnimationController.forward();
                } else {
                  _filterAnimationController.reverse();
                }
              });
            },
          ),
          // Search button
          IconButton(
            icon: Icon(_isSearching ? Icons.close : Icons.search),
            tooltip: _isSearching ? 'Cancel' : 'Search',
            onPressed: () {
              setState(() {
                _isSearching = !_isSearching;
                if (!_isSearching) {
                  _searchController.clear();
                }
              });
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // Filter panel
          SizeTransition(
            sizeFactor: _filterAnimation,
            child: _buildFilterPanel(),
          ),
          // Restaurant list
          Expanded(
            child: _buildRestaurantList(),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterPanel() {
    final theme = Theme.of(context);

    return Container(
      color: theme.colorScheme.surfaceContainerLow,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Cuisine type filter
          Text(
            'Cuisine Type',
            style: theme.textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildCuisineFilterChip(null, 'All'),
                _buildCuisineFilterChip(CuisineType.italian, 'Italian'),
                _buildCuisineFilterChip(CuisineType.japanese, 'Japanese'),
                _buildCuisineFilterChip(CuisineType.chinese, 'Chinese'),
                _buildCuisineFilterChip(CuisineType.indian, 'Indian'),
                _buildCuisineFilterChip(CuisineType.mexican, 'Mexican'),
                _buildCuisineFilterChip(CuisineType.african, 'African'),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Restaurant tier filter
          Text(
            'Restaurant Tier',
            style: theme.textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: [
                _buildTierFilterChip(null, 'All Tiers'),
                _buildTierFilterChip(RestaurantTier.highEnd, 'T-1 (High-End)'),
                _buildTierFilterChip(
                    RestaurantTier.middleClass, 'T-2 (Middle-Class)'),
                _buildTierFilterChip(RestaurantTier.lowEnd, 'T-3 (Low-End)'),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Price range filter
          Row(
            children: [
              Text(
                'Price Range',
                style: theme.textTheme.titleMedium,
              ),
              const Spacer(),
              Text(
                '\$${_minPrice.toInt()} - \$${_maxPrice.toInt()}',
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),
          const SizedBox(height: 8),
          RangeSlider(
            values: RangeValues(_minPrice, _maxPrice),
            min: 0,
            max: 500,
            divisions: 10,
            labels: RangeLabels(
              '\$${_minPrice.toInt()}',
              '\$${_maxPrice.toInt()}',
            ),
            onChanged: (values) {
              setState(() {
                _minPrice = values.start;
                _maxPrice = values.end;
              });
            },
          ),

          const SizedBox(height: 16),

          // Rating filter
          Row(
            children: [
              Text(
                'Minimum Rating',
                style: theme.textTheme.titleMedium,
              ),
              const Spacer(),
              Text(
                '${_minRating.toInt()} ★',
                style: theme.textTheme.bodyMedium,
              ),
            ],
          ),
          const SizedBox(height: 8),
          Slider(
            value: _minRating,
            min: 0,
            max: 5,
            divisions: 5,
            label: '${_minRating.toInt()} ★',
            onChanged: (value) {
              setState(() {
                _minRating = value;
              });
            },
          ),

          const SizedBox(height: 16),

          // Apply and reset buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: _resetFilters,
                  child: const Text('Reset'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: ElevatedButton(
                  onPressed: () {
                    setState(() {
                      _isFilterVisible = false;
                      _filterAnimationController.reverse();
                    });
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: theme.colorScheme.onPrimary,
                  ),
                  child: const Text('Apply'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCuisineFilterChip(CuisineType? cuisineType, String label) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Text(label),
        selected: _selectedCuisineType == cuisineType,
        onSelected: (selected) {
          setState(() {
            _selectedCuisineType = selected ? cuisineType : null;
          });
        },
      ),
    );
  }

  Widget _buildTierFilterChip(RestaurantTier? tier, String label) {
    final isSelected = _selectedTier == tier;
    final tierColor = tier?.color ?? Theme.of(context).colorScheme.primary;

    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: FilterChip(
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (tier != null) ...[
              Icon(
                tier.icon,
                size: 16,
                color: isSelected ? Colors.white : tierColor,
              ),
              const SizedBox(width: 4),
            ],
            Text(
              label,
              style: TextStyle(
                color: isSelected ? Colors.white : null,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
          ],
        ),
        selected: isSelected,
        selectedColor: tierColor,
        checkmarkColor: Colors.white,
        onSelected: (selected) {
          setState(() {
            _selectedTier = selected ? tier : null;
          });
        },
      ),
    );
  }

  void _resetFilters() {
    setState(() {
      _selectedCuisineType = null;
      _selectedTier = null;
      _minPrice = 0;
      _maxPrice = 500;
      _minRating = 0;
    });
  }

  Widget _buildRestaurantList() {
    // Use our custom restaurant provider directly for better reliability
    final restaurants = ref.watch(restaurant_provider.restaurantsProvider);

    // Debug: Print restaurant data loading
    print('🍽️ RestaurantListScreen: Loaded ${restaurants.length} restaurants');

    if (restaurants.isEmpty) {
      print('⚠️ RestaurantListScreen: No restaurants available');
      return const Center(
        child: Text('No restaurants available'),
      );
    }

    // Use our sample restaurants directly
    final allRestaurants = restaurants;

    // Apply filters
    final filteredRestaurants = allRestaurants.where((restaurant) {
      // Filter by search query
      if (_searchController.text.isNotEmpty) {
        final query = _searchController.text.toLowerCase();
        if (!restaurant.name.toLowerCase().contains(query) &&
            !restaurant.description.toLowerCase().contains(query)) {
          return false;
        }
      }

      // Filter by cuisine type
      if (_selectedCuisineType != null) {
        if (!restaurant.cuisineTypes.contains(_selectedCuisineType)) {
          return false;
        }
      }

      // Filter by restaurant tier
      if (_selectedTier != null) {
        if (restaurant.tier != _selectedTier) {
          return false;
        }
      }

      // Filter by price
      if (restaurant.price < _minPrice || restaurant.price > _maxPrice) {
        return false;
      }

      // Filter by rating
      if (restaurant.rating < _minRating) {
        return false;
      }

      return true;
    }).toList();

    // Debug: Print filtered results
    print(
        '🔍 RestaurantListScreen: Filtered to ${filteredRestaurants.length} restaurants');

    if (filteredRestaurants.isEmpty) {
      print('⚠️ RestaurantListScreen: No restaurants match filters');
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.restaurant_outlined,
              size: 64,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 16),
            const Text('No restaurants match your filters'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _resetFilters,
              child: const Text('Reset Filters'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: () async {
        // Refresh data
        ref.invalidate(restaurant_provider.restaurantsProvider);
        await Future.delayed(
            const Duration(milliseconds: 500)); // Simulate refresh
      },
      child: _isGridView
          ? GridView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(16),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio:
                    0.6, // Further reduced from 0.65 to accommodate margin + content height
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: filteredRestaurants.length,
              itemBuilder: (context, index) {
                final restaurant = filteredRestaurants[index];
                print(
                    '🏗️ RestaurantListScreen: Building GridView item $index: ${restaurant.name}');
                return RestaurantCard(
                  restaurant: restaurant,
                  isGridMode: true,
                  onTap: () => _navigateToRestaurantDetails(restaurant),
                );
              },
            )
          : ListView.builder(
              controller: _scrollController,
              padding: const EdgeInsets.all(16),
              itemCount: filteredRestaurants.length,
              itemBuilder: (context, index) {
                final restaurant = filteredRestaurants[index];
                return Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: RestaurantCard(
                    restaurant: restaurant,
                    isGridMode: false,
                    onTap: () => _navigateToRestaurantDetails(restaurant),
                  ),
                );
              },
            ),
    );
  }

  void _navigateToRestaurantDetails(Restaurant restaurant) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            RestaurantDetailsScreenEnhanced(restaurant: restaurant),
      ),
    );
  }
}
