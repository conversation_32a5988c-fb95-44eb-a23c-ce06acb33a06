// Dart imports
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/travel/restaurant_reservation.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/providers/auth_provider.dart';
import 'package:culture_connect/providers/travel/restaurant_reservation_provider.dart';
import 'package:culture_connect/screens/payment/universal_payment_screen.dart';
import 'package:culture_connect/services/logging_service.dart';
import 'package:culture_connect/widgets/common/error_view.dart';
import 'package:culture_connect/widgets/common/loading_indicator.dart';
import 'package:culture_connect/widgets/travel/restaurant/calendar_day_picker.dart';
import 'package:culture_connect/widgets/travel/restaurant/party_size_selector.dart';
import 'package:culture_connect/widgets/travel/restaurant/time_slot_selector.dart';
import 'package:culture_connect/models/travel/restaurant_tier.dart';

/// Screen for making a restaurant reservation
class RestaurantReservationScreen extends ConsumerStatefulWidget {
  /// The restaurant to make a reservation for
  final Restaurant restaurant;

  /// Creates a new restaurant reservation screen
  const RestaurantReservationScreen({
    super.key,
    required this.restaurant,
  });

  @override
  ConsumerState<RestaurantReservationScreen> createState() =>
      _RestaurantReservationScreenState();
}

class _RestaurantReservationScreenState
    extends ConsumerState<RestaurantReservationScreen> {
  final _formKey = GlobalKey<FormState>();
  final _specialRequestsController = TextEditingController();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _emailController = TextEditingController();
  final _logger = LoggingService();

  int _currentStep = 0;
  bool _isLoading = false;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();

    // Set initial values for providers
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        _logger.info('RestaurantReservationScreen',
            'Initializing reservation screen for restaurant: ${widget.restaurant.id}');

        // Set selected date to today
        ref.read(selectedReservationDateProvider.notifier).state =
            DateTime.now();

        // Set selected party size to 2
        ref.read(selectedPartySizeProvider.notifier).state = 2;

        // Clear selected time slot
        ref.read(selectedTimeSlotProvider.notifier).state = null;

        // Clear special requests
        ref.read(specialRequestsProvider.notifier).state = '';

        // Pre-fill user information if available
        final user = ref.read(authStateProvider).user;
        if (user != null) {
          _nameController.text = user.fullName;
          _emailController.text = user.email;
          _phoneController.text = user.phoneNumber;

          _logger.info('RestaurantReservationScreen',
              'Pre-filled user information for ${user.id}');
        }
      } catch (e, stackTrace) {
        _logger.error('RestaurantReservationScreen',
            'Error initializing reservation screen', e, stackTrace);

        if (mounted) {
          setState(() {
            _errorMessage =
                'Failed to initialize reservation screen: ${e.toString()}';
          });
        }
      }
    });
  }

  @override
  void dispose() {
    _specialRequestsController.dispose();
    _nameController.dispose();
    _phoneController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Make a Reservation'),
      ),
      body: _isLoading
          ? const Center(child: LoadingIndicator())
          : _errorMessage != null
              ? ErrorView(
                  error: _errorMessage!,
                  onRetry: () => setState(() => _errorMessage = null),
                )
              : Stepper(
                  currentStep: _currentStep,
                  onStepContinue: _handleContinue,
                  onStepCancel: _handleCancel,
                  controlsBuilder: (context, details) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 16),
                      child: Row(
                        children: [
                          Expanded(
                            flex: 3, // Increased flex for more prominence
                            child: Container(
                              decoration: BoxDecoration(
                                gradient: const LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Color(0xFFFF385C), // AppTheme.primaryColor
                                    Color(0xFFE8294A), // AppTheme.primaryDark
                                  ],
                                ),
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color: const Color(0xFFFF385C)
                                        .withAlpha(77), // 30% opacity
                                    blurRadius: 8,
                                    offset: const Offset(0, 4),
                                  ),
                                ],
                              ),
                              child: ElevatedButton(
                                onPressed: details.onStepContinue,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.transparent,
                                  foregroundColor: Colors.white,
                                  shadowColor: Colors.transparent,
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 16,
                                    horizontal: 24,
                                  ),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(16),
                                  ),
                                ),
                                child: Text(
                                  _currentStep == 2
                                      ? 'Complete Reservation'
                                      : 'Continue',
                                  style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                  ),
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          if (_currentStep > 0)
                            Expanded(
                              flex: 1, // Reduced flex for smaller width
                              child: OutlinedButton(
                                onPressed: details.onStepCancel,
                                style: OutlinedButton.styleFrom(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 12),
                                ),
                                child: Text(
                                  'Back',
                                  style: theme.textTheme.titleMedium,
                                ),
                              ),
                            ),
                        ],
                      ),
                    );
                  },
                  steps: [
                    _buildDateAndTimeStep(),
                    _buildPartyDetailsStep(),
                    _buildContactInfoStep(),
                  ],
                ),
    );
  }

  Step _buildDateAndTimeStep() {
    final selectedDate = ref.watch(selectedReservationDateProvider);
    final selectedTimeSlot = ref.watch(selectedTimeSlotProvider);

    return Step(
      title: const Text('Select Date & Time'),
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Date picker
          Text(
            'Date',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          CalendarDayPicker(
            selectedDate: selectedDate,
            onDateSelected: (date) {
              ref.read(selectedReservationDateProvider.notifier).state = date;
              // Clear selected time slot when date changes
              ref.read(selectedTimeSlotProvider.notifier).state = null;
            },
            minDate: DateTime.now(),
            maxDate: DateTime.now().add(const Duration(days: 30)),
          ),
          const SizedBox(height: 24),

          // Time slot picker
          Text(
            'Time',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          Consumer(
            builder: (context, ref, child) {
              final availableSlotsAsync = ref.watch(
                availableTimeSlotsProvider((
                  restaurantId: widget.restaurant.id,
                  date: selectedDate,
                )),
              );

              return availableSlotsAsync.when(
                data: (slots) {
                  if (slots.isEmpty) {
                    return const Center(
                      child: Padding(
                        padding: EdgeInsets.all(16.0),
                        child: Text('No available time slots for this date'),
                      ),
                    );
                  }

                  return TimeSlotSelector(
                    timeSlots: slots,
                    selectedTimeSlot: selectedTimeSlot,
                    onTimeSlotSelected: (slot) {
                      ref.read(selectedTimeSlotProvider.notifier).state = slot;
                    },
                  );
                },
                loading: () => const Center(child: LoadingIndicator()),
                error: (error, stackTrace) => ErrorView(
                  error: 'Failed to load time slots: $error',
                  onRetry: () {
                    _logger.warning(
                        'RestaurantReservationScreen',
                        'Failed to load time slots, retrying',
                        error,
                        stackTrace);
                    ref.invalidate(
                      availableTimeSlotsProvider((
                        restaurantId: widget.restaurant.id,
                        date: selectedDate,
                      )),
                    );
                  },
                ),
              );
            },
          ),
        ],
      ),
      isActive: _currentStep == 0,
      state: _currentStep > 0 ? StepState.complete : StepState.indexed,
    );
  }

  Step _buildPartyDetailsStep() {
    final selectedPartySize = ref.watch(selectedPartySizeProvider);

    return Step(
      title: const Text('Number of Guests'),
      content: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Party size selector
          Text(
            'Number of People',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          PartySizeSelector(
            partySize: selectedPartySize,
            onPartySizeChanged: (size) {
              ref.read(selectedPartySizeProvider.notifier).state = size;
            },
            minSize: 1,
            maxSize: 20,
          ),
          const SizedBox(height: 24),

          // Special requests
          Text(
            'Special Requests (Optional)',
            style: Theme.of(context).textTheme.titleMedium,
          ),
          const SizedBox(height: 8),
          TextFormField(
            controller: _specialRequestsController,
            decoration: InputDecoration(
              hintText:
                  'E.g., Window seat, birthday celebration, dietary needs...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(
                  color: Color(0xFFDDDDDD), // AppTheme.borderColor
                  width: 1.0,
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(
                  color: Color(0xFFDDDDDD), // AppTheme.borderColor
                  width: 1.0,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: const BorderSide(
                  color: Color(0xFFFF385C), // AppTheme.primaryColor
                  width: 2.0,
                ),
              ),
              filled: true,
              fillColor: Colors.white,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
            ),
            maxLines: 3,
            onChanged: (value) {
              ref.read(specialRequestsProvider.notifier).state = value;
            },
          ),
        ],
      ),
      isActive: _currentStep == 1,
      state: _currentStep > 1 ? StepState.complete : StepState.indexed,
    );
  }

  Step _buildContactInfoStep() {
    return Step(
      title: const Text('Contact Information'),
      content: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Name
            TextFormField(
              controller: _nameController,
              decoration: InputDecoration(
                labelText: 'Full Name',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(
                    color: Color(0xFFDDDDDD), // AppTheme.borderColor
                    width: 1.0,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(
                    color: Color(0xFFDDDDDD), // AppTheme.borderColor
                    width: 1.0,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(
                    color: Color(0xFFFF385C), // AppTheme.primaryColor
                    width: 2.0,
                  ),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(
                    color: Color(0xFFC13515), // AppTheme.errorColor
                    width: 1.0,
                  ),
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
                floatingLabelBehavior: FloatingLabelBehavior.auto,
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your name';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Phone
            TextFormField(
              controller: _phoneController,
              decoration: InputDecoration(
                labelText: 'Phone Number',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(
                    color: Color(0xFFDDDDDD), // AppTheme.borderColor
                    width: 1.0,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(
                    color: Color(0xFFDDDDDD), // AppTheme.borderColor
                    width: 1.0,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(
                    color: Color(0xFFFF385C), // AppTheme.primaryColor
                    width: 2.0,
                  ),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(
                    color: Color(0xFFC13515), // AppTheme.errorColor
                    width: 1.0,
                  ),
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
                floatingLabelBehavior: FloatingLabelBehavior.auto,
              ),
              keyboardType: TextInputType.phone,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your phone number';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Email
            TextFormField(
              controller: _emailController,
              decoration: InputDecoration(
                labelText: 'Email',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(
                    color: Color(0xFFDDDDDD), // AppTheme.borderColor
                    width: 1.0,
                  ),
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(
                    color: Color(0xFFDDDDDD), // AppTheme.borderColor
                    width: 1.0,
                  ),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(
                    color: Color(0xFFFF385C), // AppTheme.primaryColor
                    width: 2.0,
                  ),
                ),
                errorBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(
                    color: Color(0xFFC13515), // AppTheme.errorColor
                    width: 1.0,
                  ),
                ),
                filled: true,
                fillColor: Colors.white,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
                floatingLabelBehavior: FloatingLabelBehavior.auto,
              ),
              keyboardType: TextInputType.emailAddress,
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return 'Please enter your email';
                }
                if (!value.contains('@') || !value.contains('.')) {
                  return 'Please enter a valid email';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // Reservation summary
            _buildReservationSummary(),
          ],
        ),
      ),
      isActive: _currentStep == 2,
      state: _currentStep > 2 ? StepState.complete : StepState.indexed,
    );
  }

  Widget _buildReservationSummary() {
    final theme = Theme.of(context);
    final selectedDate = ref.watch(selectedReservationDateProvider);
    final selectedTimeSlot = ref.watch(selectedTimeSlotProvider);
    final selectedPartySize = ref.watch(selectedPartySizeProvider);
    final specialRequests = ref.watch(specialRequestsProvider);

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFFFFBFE), // Very light pink
            Color(0xFFF8F9FA), // Light grey
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13), // 5% opacity - subtle
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
          BoxShadow(
            color: const Color(0xFFFF385C).withAlpha(26), // 10% primary color
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: Colors.black.withAlpha(8), // 3% opacity - very subtle
            blurRadius: 32,
            offset: const Offset(0, 16),
          ),
        ],
        border: Border.all(
          color: const Color(0xFFFF385C).withAlpha(51), // 20% primary color
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Color(0xFFFF385C), // AppTheme.primaryColor
                        Color(0xFFE8294A), // AppTheme.primaryDark
                        Color(0xFFD63384), // Deeper pink
                      ],
                      stops: [0.0, 0.6, 1.0],
                    ),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: const Color(0xFFFF385C)
                            .withAlpha(77), // 30% opacity
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.restaurant_menu,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Reservation Summary',
                        style: theme.textTheme.titleLarge?.copyWith(
                          color: const Color(0xFF1A1A1A),
                          fontWeight: FontWeight.w700,
                          letterSpacing: -0.5,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Review your booking details',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: const Color(0xFF6B7280),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Container(
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.transparent,
                    const Color(0xFFFF385C).withAlpha(51), // 20% opacity
                    Colors.transparent,
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),
            _buildSummaryRow('Restaurant', widget.restaurant.name),
            _buildSummaryRow(
                'Date', DateFormat('EEEE, MMMM d, yyyy').format(selectedDate)),
            if (selectedTimeSlot != null)
              _buildSummaryRow('Time', selectedTimeSlot.formatted),
            _buildSummaryRow('Party Size',
                '$selectedPartySize ${selectedPartySize == 1 ? 'person' : 'people'}'),
            if (specialRequests.isNotEmpty)
              _buildSummaryRow('Special Requests', specialRequests),
          ],
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value) {
    final theme = Theme.of(context);

    // Get appropriate icon for each label
    IconData getIconForLabel(String label) {
      switch (label.toLowerCase()) {
        case 'restaurant':
          return Icons.restaurant;
        case 'date':
          return Icons.calendar_today;
        case 'time':
          return Icons.access_time;
        case 'party size':
          return Icons.people;
        case 'special requests':
          return Icons.note_alt;
        default:
          return Icons.info_outline;
      }
    }

    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            const Color(0xFFFFFBFE), // Very light pink tint
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: const Color(0xFFFF385C).withAlpha(26), // 10% primary color
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(8), // 3% opacity
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: const Color(0xFFFF385C).withAlpha(26), // 10% primary color
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              getIconForLabel(label),
              size: 16,
              color: const Color(0xFFFF385C),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: theme.textTheme.bodySmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF6B7280),
                    letterSpacing: 0.5,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: const Color(0xFF1A1A1A),
                    fontWeight: FontWeight.w600,
                    fontSize: 15,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleContinue() {
    try {
      _logger.info('RestaurantReservationScreen',
          'Handling continue button at step $_currentStep');

      // Validate current step
      if (_currentStep == 0) {
        // Validate date and time
        final selectedTimeSlot = ref.read(selectedTimeSlotProvider);
        if (selectedTimeSlot == null) {
          _logger.warning(
              'RestaurantReservationScreen', 'Time slot not selected');

          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Please select a time slot')),
          );
          return;
        }
      } else if (_currentStep == 2) {
        // Validate contact info and submit reservation
        if (_formKey.currentState?.validate() ?? false) {
          _submitReservation();
          return;
        } else {
          _logger.warning(
              'RestaurantReservationScreen', 'Contact form validation failed');
          return;
        }
      }

      // Move to next step
      setState(() {
        _currentStep += 1;
      });

      _logger.info(
          'RestaurantReservationScreen', 'Advanced to step $_currentStep');
    } catch (e, stackTrace) {
      _logger.error('RestaurantReservationScreen',
          'Error handling continue button', e, stackTrace);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('An error occurred: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _handleCancel() {
    try {
      _logger.info('RestaurantReservationScreen',
          'Handling cancel button at step $_currentStep');

      if (_currentStep > 0) {
        setState(() {
          _currentStep -= 1;
        });

        _logger.info(
            'RestaurantReservationScreen', 'Returned to step $_currentStep');
      } else {
        _logger.info('RestaurantReservationScreen',
            'Already at first step, cannot go back');
      }
    } catch (e, stackTrace) {
      _logger.error('RestaurantReservationScreen',
          'Error handling cancel button', e, stackTrace);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('An error occurred: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _submitReservation() async {
    _logger.info('RestaurantReservationScreen',
        'Submitting reservation for restaurant: ${widget.restaurant.id}');

    final selectedDate = ref.read(selectedReservationDateProvider);
    final selectedTimeSlot = ref.read(selectedTimeSlotProvider);
    final selectedPartySize = ref.read(selectedPartySizeProvider);
    final specialRequests = ref.read(specialRequestsProvider);
    final user = ref.read(authStateProvider).user;

    // Validate required form data
    if (selectedTimeSlot == null) {
      final errorMsg =
          'Missing required information for reservation: time slot not selected';
      _logger.warning('RestaurantReservationScreen', errorMsg);

      if (mounted) {
        setState(() {
          _errorMessage = 'Please select a time slot for your reservation';
        });
      }
      return;
    }

    // Validate contact information from form
    if (_nameController.text.trim().isEmpty ||
        _emailController.text.trim().isEmpty ||
        _phoneController.text.trim().isEmpty) {
      final errorMsg = 'Missing required contact information for reservation';
      _logger.warning('RestaurantReservationScreen', errorMsg);

      if (mounted) {
        setState(() {
          _errorMessage = 'Please fill in all contact information fields';
        });
      }
      return;
    }

    if (mounted) {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });
    }

    try {
      final userId =
          user?.id ?? 'guest-${DateTime.now().millisecondsSinceEpoch}';
      _logger.info('RestaurantReservationScreen',
          'Creating reservation for $userId at ${widget.restaurant.name} on ${selectedDate.toIso8601String()} at ${selectedTimeSlot.formatted}');

      await ref
          .read(restaurantReservationNotifierProvider.notifier)
          .createReservation(
            restaurantId: widget.restaurant.id,
            restaurantName: widget.restaurant.name,
            date: selectedDate,
            timeSlot: selectedTimeSlot,
            partySize: selectedPartySize,
            specialRequests: specialRequests,
            userId: userId,
            userName: _nameController.text,
            contactPhone: _phoneController.text,
            contactEmail: _emailController.text,
            depositAmount: _calculateDepositAmount(),
            depositRequired: widget.restaurant.requiresDeposit,
            optedForDeposit:
                false, // TODO: Add UI for optional deposit selection
          );

      final reservation = ref.read(restaurantReservationNotifierProvider);

      if (!mounted) {
        _logger.warning('RestaurantReservationScreen',
            'Widget unmounted after creating reservation');
        return;
      }

      if (reservation.hasValue && reservation.value != null) {
        _logger.info('RestaurantReservationScreen',
            'Reservation created successfully with ID: ${reservation.value!.id}');

        // Navigate to payment screen first
        _navigateToPayment(reservation.value!);
      } else {
        _logger.error(
            'RestaurantReservationScreen',
            'Failed to create reservation - provider returned null or error state',
            reservation.error);

        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to create reservation';
        });
      }
    } catch (e, stackTrace) {
      _logger.error('RestaurantReservationScreen', 'Error creating reservation',
          e, stackTrace);

      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Error: ${e.toString()}';
        });
      }
    }
  }

  void _navigateToPayment(RestaurantReservation reservation) {
    // Handle navigation using reservation contact info
    // No authentication required for payment screen

    // Create TimeSlot from RestaurantTimeSlot
    final startDateTime = DateTime(
      reservation.date.year,
      reservation.date.month,
      reservation.date.day,
      reservation.timeSlot.startTime.hour,
      reservation.timeSlot.startTime.minute,
    );
    final endDateTime = DateTime(
      reservation.date.year,
      reservation.date.month,
      reservation.date.day,
      reservation.timeSlot.endTime.hour,
      reservation.timeSlot.endTime.minute,
    );

    // Create booking object for payment
    final booking = Booking(
      id: reservation.id,
      experienceId: widget.restaurant.id,
      date: reservation.date,
      timeSlot: TimeSlot(
        startTime: startDateTime,
        endTime: endDateTime,
        isAvailable: true,
      ),
      participantCount: reservation.partySize,
      totalAmount: _calculateDepositAmount(),
      status: BookingStatus.pending,
      specialRequirements: reservation.specialRequests,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    try {
      _logger.info('RestaurantReservationScreen',
          'Navigating to payment screen for reservation: ${reservation.id}');

      Navigator.pushReplacement(
        context,
        MaterialPageRoute(
          builder: (context) => UniversalPaymentScreen(
            booking: booking,
            userEmail: reservation.contactEmail,
            userName: reservation.userName,
            userPhone: reservation.contactPhone,
            bookingDetails: {
              'reservation': reservation,
              'restaurant': widget.restaurant,
            },
          ),
        ),
      );
    } catch (e, stackTrace) {
      _logger.error('RestaurantReservationScreen',
          'Failed to navigate to payment screen: $e', stackTrace);

      if (mounted) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Failed to proceed to payment. Please try again.';
        });
      }

      // Show user-friendly error dialog
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Navigation Error'),
            content: const Text(
              'We encountered an issue proceeding to payment. Please try again or contact support if the problem persists.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('OK'),
              ),
            ],
          ),
        );
      }
    }
  }

  /// Calculate the deposit amount based on restaurant tier and user selection
  double _calculateDepositAmount() {
    final tier = widget.restaurant.tier;
    final depositPolicy = widget.restaurant.depositPolicy;

    // For high-end restaurants, deposit is always required
    if (tier == RestaurantTier.highEnd) {
      return depositPolicy.amount;
    }

    // For middle-class restaurants, check if user opted for deposit
    // (This would be determined by a UI selection - for now, default to no deposit)
    if (tier == RestaurantTier.middleClass) {
      // TODO: Add UI for optional deposit selection
      return 0.0; // Default to no deposit for now
    }

    // Low-end restaurants have no deposit
    return 0.0;
  }
}
