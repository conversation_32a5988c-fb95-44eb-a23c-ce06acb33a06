import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/models/booking.dart';

import 'package:culture_connect/config/payment_simulation_config.dart';
import 'package:culture_connect/services/payment_router_service.dart';
import 'package:culture_connect/widgets/payment/payment_modals.dart';

/// Universal Payment Screen for all booking types
///
/// This screen dynamically displays relevant booking details based on the payment context
/// (flight-only, hotel-only, multi-service adventure trips, etc.) and provides secure payment options.
///
/// Features:
/// - Dynamic content rendering based on booking type
/// - Secure payment form with validation
/// - Integration with existing payment simulation system
/// - Unique color scheme for payment security
/// - Responsive design across different screen sizes
class UniversalPaymentScreen extends ConsumerStatefulWidget {
  final Booking booking;
  final String userEmail;
  final String userName;
  final String? userPhone;
  final Map<String, dynamic>? bookingDetails;

  const UniversalPaymentScreen({
    super.key,
    required this.booking,
    required this.userEmail,
    required this.userName,
    this.userPhone,
    this.bookingDetails,
  });

  @override
  ConsumerState<UniversalPaymentScreen> createState() =>
      _UniversalPaymentScreenState();
}

class _UniversalPaymentScreenState extends ConsumerState<UniversalPaymentScreen>
    with TickerProviderStateMixin {
  // Payment-specific color scheme for security and trust
  static const Color _paymentPrimary = Color(0xFF1A365D); // Deep blue for trust
  static const Color _paymentSecondary = Color(0xFF2D3748); // Dark gray
  static const Color _paymentAccent = Color(0xFF38B2AC); // Teal for success
  static const Color _paymentBackground = Color(0xFFF7FAFC); // Light blue-gray
  static const Color _paymentSurface = Color(0xFFFFFFFF); // Pure white
  static const Color _paymentError = Color(0xFFE53E3E); // Red for errors
  static const Color _paymentSuccess = Color(0xFF38A169); // Green for success

  // State variables
  bool _isProcessing = false;

  // Animation controllers
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _fadeController.forward();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _paymentBackground,
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(AppTheme.spacingMd),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildOrderSummaryCard(),
                    const SizedBox(height: AppTheme.spacingLg),
                    _buildPaymentMethodsCard(),
                    const SizedBox(height: AppTheme.spacingLg),
                    _buildSecurityInfo(),
                    const SizedBox(height: AppTheme.spacingXl),
                  ],
                ),
              ),
            ),
            _buildBottomActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            _paymentPrimary,
            _paymentPrimary.withOpacity(0.8),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: _paymentPrimary.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: SafeArea(
        bottom: false,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 24),
          child: Row(
            children: [
              Container(
                width: 44,
                height: 44,
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(22),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.arrow_back_ios),
                  color: Colors.white,
                  iconSize: 20,
                ),
              ),
              const Expanded(
                child: Column(
                  children: [
                    Text(
                      'Secure Payment',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                        letterSpacing: -0.5,
                      ),
                    ),
                    SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.security,
                          color: Colors.white,
                          size: 16,
                        ),
                        SizedBox(width: 4),
                        Text(
                          'SSL Encrypted',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.white,
                            letterSpacing: -0.2,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 56), // Balance the back button
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildOrderSummaryCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: _paymentSurface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppTheme.shadowMedium,
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingLg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.receipt_long,
                  color: _paymentPrimary,
                  size: 24,
                ),
                const SizedBox(width: AppTheme.spacingSm),
                const Text(
                  'Order Summary',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: AppTheme.textPrimaryColor,
                    letterSpacing: -0.3,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingMd),
            _buildDynamicBookingDetails(),
            const Divider(height: AppTheme.spacingLg * 2),
            _buildPricingBreakdown(),
          ],
        ),
      ),
    );
  }

  Widget _buildDynamicBookingDetails() {
    // Determine booking type and render appropriate content
    final bookingType = _getBookingType();

    switch (bookingType) {
      case 'flight':
        return _buildFlightDetails();
      case 'hotel':
        return _buildHotelDetails();
      case 'car':
        return _buildCarDetails();
      case 'adventure':
        return _buildAdventureDetails();
      case 'experience':
        return _buildExperienceDetails();
      default:
        return _buildGenericDetails();
    }
  }

  String _getBookingType() {
    // Analyze booking data to determine type
    if (widget.bookingDetails != null) {
      final details = widget.bookingDetails!;
      if (details.containsKey('flightNumber') ||
          details.containsKey('airline')) {
        return 'flight';
      } else if (details.containsKey('hotelName') ||
          details.containsKey('roomType')) {
        return 'hotel';
      } else if (details.containsKey('carModel') ||
          details.containsKey('rentalDays')) {
        return 'car';
      } else if (details.containsKey('services') &&
          details['services'] is List) {
        return 'adventure';
      }
    }

    // Fallback to booking experience ID analysis
    if (widget.booking.experienceId.contains('flight')) return 'flight';
    if (widget.booking.experienceId.contains('hotel')) return 'hotel';
    if (widget.booking.experienceId.contains('car')) return 'car';
    if (widget.booking.experienceId.contains('adventure')) return 'adventure';

    return 'experience';
  }

  Widget _buildFlightDetails() {
    final details = widget.bookingDetails ?? {};
    final flightNumber = details['flightNumber'] ?? 'AA1234';
    final airline = details['airline'] ?? 'American Airlines';
    final departure = details['departure'] ?? 'New York (JFK)';
    final arrival = details['arrival'] ?? 'Tokyo (NRT)';
    final date = details['date'] ?? 'Dec 25, 2024';
    final passengers = details['passengers'] ?? widget.booking.participantCount;
    final seatClass = details['class'] ?? 'Economy';
    final selectedSeat = details['selectedSeat'];
    final duration = details['duration'] ?? '14h 15m';

    return Column(
      children: [
        _buildDetailRow(
          icon: Icons.flight_takeoff,
          iconColor: _paymentAccent,
          title: '$airline $flightNumber',
          subtitle: '$departure → $arrival',
        ),
        const SizedBox(height: AppTheme.spacingSm),
        _buildDetailRow(
          icon: Icons.calendar_today,
          iconColor: _paymentPrimary,
          title: 'Departure Date',
          subtitle: '$date • $duration',
        ),
        const SizedBox(height: AppTheme.spacingSm),
        _buildDetailRow(
          icon: Icons.people,
          iconColor: _paymentSecondary,
          title: 'Passengers & Class',
          subtitle:
              '$passengers passenger${passengers > 1 ? 's' : ''} • $seatClass',
        ),
        if (selectedSeat != null) ...[
          const SizedBox(height: AppTheme.spacingSm),
          _buildDetailRow(
            icon: Icons.airline_seat_recline_normal,
            iconColor: _paymentAccent,
            title: 'Selected Seat',
            subtitle: 'Seat $selectedSeat',
          ),
        ],
      ],
    );
  }

  Widget _buildHotelDetails() {
    final details = widget.bookingDetails ?? {};
    final hotelName = details['hotelName'] ?? 'Grand Hotel Tokyo';
    final roomType = details['roomType'] ?? 'Deluxe Room';
    final checkIn = details['checkIn'] ?? 'Dec 25, 2024';
    final checkOut = details['checkOut'] ?? 'Dec 28, 2024';
    final guests = details['guests'] ?? widget.booking.participantCount;
    final nights = details['nights'] ?? 3;

    return Column(
      children: [
        _buildDetailRow(
          icon: Icons.hotel,
          iconColor: _paymentAccent,
          title: hotelName,
          subtitle: roomType,
        ),
        const SizedBox(height: AppTheme.spacingSm),
        _buildDetailRow(
          icon: Icons.calendar_today,
          iconColor: _paymentPrimary,
          title: 'Stay Duration',
          subtitle:
              '$checkIn - $checkOut ($nights night${nights > 1 ? 's' : ''})',
        ),
        const SizedBox(height: AppTheme.spacingSm),
        _buildDetailRow(
          icon: Icons.people,
          iconColor: _paymentSecondary,
          title: 'Guests',
          subtitle: '$guests guest${guests > 1 ? 's' : ''}',
        ),
      ],
    );
  }

  Widget _buildCarDetails() {
    final details = widget.bookingDetails ?? {};
    final carModel = details['carModel'] ?? 'Toyota Camry';
    final carType = details['carType'] ?? 'Sedan';
    final pickupDate = details['pickupDate'] ?? 'Dec 25, 2024';
    final returnDate = details['returnDate'] ?? 'Dec 28, 2024';
    final days = details['rentalDays'] ?? 3;
    final location = details['location'] ?? 'Tokyo Airport';

    return Column(
      children: [
        _buildDetailRow(
          icon: Icons.directions_car,
          iconColor: _paymentAccent,
          title: carModel,
          subtitle: '$carType • $location',
        ),
        const SizedBox(height: AppTheme.spacingSm),
        _buildDetailRow(
          icon: Icons.calendar_today,
          iconColor: _paymentPrimary,
          title: 'Rental Period',
          subtitle:
              '$pickupDate - $returnDate ($days day${days > 1 ? 's' : ''})',
        ),
      ],
    );
  }

  Widget _buildAdventureDetails() {
    final details = widget.bookingDetails ?? {};
    final services =
        details['services'] as List? ?? ['Flight', 'Hotel', 'Car Rental'];
    final destination = details['destination'] ?? 'Tokyo, Japan';
    final duration = details['duration'] ?? '7 days';
    final travelers = details['travelers'] ?? widget.booking.participantCount;

    return Column(
      children: [
        _buildDetailRow(
          icon: Icons.explore,
          iconColor: _paymentAccent,
          title: 'Adventure Package',
          subtitle: destination,
        ),
        const SizedBox(height: AppTheme.spacingSm),
        _buildDetailRow(
          icon: Icons.schedule,
          iconColor: _paymentPrimary,
          title: 'Duration',
          subtitle: duration,
        ),
        const SizedBox(height: AppTheme.spacingSm),
        _buildDetailRow(
          icon: Icons.people,
          iconColor: _paymentSecondary,
          title: 'Travelers',
          subtitle: '$travelers traveler${travelers > 1 ? 's' : ''}',
        ),
        const SizedBox(height: AppTheme.spacingSm),
        Container(
          padding: const EdgeInsets.all(AppTheme.spacingSm),
          decoration: BoxDecoration(
            color: _paymentAccent.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Included Services:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              const SizedBox(height: AppTheme.spacingXs),
              ...services.map((service) => Padding(
                    padding: const EdgeInsets.only(bottom: 2),
                    child: Row(
                      children: [
                        Icon(
                          Icons.check_circle,
                          size: 16,
                          color: _paymentAccent,
                        ),
                        const SizedBox(width: AppTheme.spacingXs),
                        Text(
                          service.toString(),
                          style: const TextStyle(
                            fontSize: 13,
                            color: AppTheme.textSecondaryColor,
                          ),
                        ),
                      ],
                    ),
                  )),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildExperienceDetails() {
    final details = widget.bookingDetails ?? {};
    final experienceName = details['experienceName'] ?? 'Cultural Experience';
    final location = details['location'] ?? 'Tokyo, Japan';
    final date = details['date'] ?? 'Dec 25, 2024';
    final duration = details['duration'] ?? '3 hours';
    final participants = widget.booking.participantCount;

    return Column(
      children: [
        _buildDetailRow(
          icon: Icons.star,
          iconColor: _paymentAccent,
          title: experienceName,
          subtitle: location,
        ),
        const SizedBox(height: AppTheme.spacingSm),
        _buildDetailRow(
          icon: Icons.calendar_today,
          iconColor: _paymentPrimary,
          title: 'Date & Duration',
          subtitle: '$date • $duration',
        ),
        const SizedBox(height: AppTheme.spacingSm),
        _buildDetailRow(
          icon: Icons.people,
          iconColor: _paymentSecondary,
          title: 'Participants',
          subtitle: '$participants participant${participants > 1 ? 's' : ''}',
        ),
      ],
    );
  }

  Widget _buildGenericDetails() {
    return Column(
      children: [
        _buildDetailRow(
          icon: Icons.confirmation_number,
          iconColor: _paymentAccent,
          title: 'Booking',
          subtitle: 'Experience Booking',
        ),
        const SizedBox(height: AppTheme.spacingSm),
        _buildDetailRow(
          icon: Icons.people,
          iconColor: _paymentSecondary,
          title: 'Participants',
          subtitle:
              '${widget.booking.participantCount} participant${widget.booking.participantCount > 1 ? 's' : ''}',
        ),
      ],
    );
  }

  Widget _buildDetailRow({
    required IconData icon,
    required Color iconColor,
    required String title,
    required String subtitle,
  }) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: iconColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            icon,
            color: iconColor,
            size: 20,
          ),
        ),
        const SizedBox(width: AppTheme.spacingSm),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              Text(
                subtitle,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPricingBreakdown() {
    final details = widget.bookingDetails ?? {};
    final basePrice =
        details['basePrice']?.toDouble() ?? (widget.booking.totalAmount * 0.85);
    final seatFee = details['seatFee']?.toDouble() ?? 0.0;
    final taxes = widget.booking.totalAmount - basePrice - seatFee;

    return Column(
      children: [
        _buildPriceRow('Base Fare', basePrice),
        if (seatFee > 0) ...[
          const SizedBox(height: AppTheme.spacingSm),
          _buildPriceRow('Seat Selection Fee', seatFee),
        ],
        const SizedBox(height: AppTheme.spacingSm),
        _buildPriceRow('Taxes & Fees', taxes),
        const SizedBox(height: AppTheme.spacingMd),
        Container(
          padding: const EdgeInsets.all(AppTheme.spacingSm),
          decoration: BoxDecoration(
            color: _paymentSuccess.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Total Amount',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w700,
                  color: AppTheme.textPrimaryColor,
                ),
              ),
              Text(
                '\$${widget.booking.totalAmount.toStringAsFixed(2)}',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.w800,
                  color: _paymentSuccess,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPriceRow(String label, double amount) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 15,
            color: AppTheme.textSecondaryColor,
          ),
        ),
        Text(
          '\$${amount.toStringAsFixed(2)}',
          style: const TextStyle(
            fontSize: 15,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentMethodsCard() {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: _paymentSurface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: AppTheme.shadowMedium,
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingLg),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.payment,
                  color: _paymentPrimary,
                  size: 24,
                ),
                const SizedBox(width: AppTheme.spacingSm),
                const Text(
                  'Payment Method',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w700,
                    color: AppTheme.textPrimaryColor,
                    letterSpacing: -0.3,
                  ),
                ),
                const Spacer(),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _paymentSuccess.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.security,
                        size: 14,
                        color: _paymentSuccess,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        'Secure',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: _paymentSuccess,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppTheme.spacingLg),

            // Add New Card Option
            _buildAddNewCardOption(),
            const SizedBox(height: AppTheme.spacingMd),

            // Digital Wallet Options
            _buildDigitalWalletOptions(),
            const SizedBox(height: AppTheme.spacingMd),

            // Cryptocurrency Option
            _buildCryptocurrencyOption(),
          ],
        ),
      ),
    );
  }

  Widget _buildAddNewCardOption() {
    return GestureDetector(
      onTap: () => PaymentModals.showAddCardModal(context),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppTheme.spacingMd),
        decoration: BoxDecoration(
          border: Border.all(
            color: _paymentPrimary,
            width: 2,
            style: BorderStyle.solid,
          ),
          borderRadius: BorderRadius.circular(12),
          color: _paymentPrimary.withOpacity(0.05),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: _paymentPrimary.withOpacity(0.1),
                borderRadius: BorderRadius.circular(24),
              ),
              child: Icon(
                Icons.add,
                color: _paymentPrimary,
                size: 24,
              ),
            ),
            const SizedBox(width: AppTheme.spacingMd),
            const Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Add New Card',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimaryColor,
                    ),
                  ),
                  Text(
                    'Credit or Debit Card',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppTheme.textSecondaryColor,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: _paymentPrimary,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDigitalWalletOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Digital Wallets',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textSecondaryColor,
          ),
        ),
        const SizedBox(height: AppTheme.spacingSm),
        _buildPaymentOption(
          icon: Icons.phone_android,
          title: 'Apple Pay',
          subtitle: 'Touch ID or Face ID',
          onTap: () => PaymentModals.showWalletModal(context, 'Apple Pay'),
          gradient: const LinearGradient(
            colors: [Color(0xFF000000), Color(0xFF434343)],
          ),
        ),
        const SizedBox(height: AppTheme.spacingSm),
        _buildPaymentOption(
          icon: Icons.account_balance_wallet,
          title: 'Google Pay',
          subtitle: 'Quick & Secure',
          onTap: () => PaymentModals.showWalletModal(context, 'Google Pay'),
          gradient: const LinearGradient(
            colors: [Color(0xFF4285F4), Color(0xFF34A853)],
          ),
        ),
      ],
    );
  }

  Widget _buildCryptocurrencyOption() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Cryptocurrency',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textSecondaryColor,
          ),
        ),
        const SizedBox(height: AppTheme.spacingSm),
        _buildPaymentOption(
          icon: Icons.currency_bitcoin,
          title: 'Bitcoin',
          subtitle: 'Pay with BTC • Instant',
          onTap: () => PaymentModals.showCryptoModal(
              context, widget.booking.totalAmount),
          gradient: const LinearGradient(
            colors: [Color(0xFFF7931A), Color(0xFFFFB347)],
          ),
          badge: 'NEW',
        ),
      ],
    );
  }

  Widget _buildPaymentOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required LinearGradient gradient,
    String? badge,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppTheme.spacingMd),
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: gradient.colors.first.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                icon,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: AppTheme.spacingMd),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    subtitle,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            if (badge != null) ...[
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  badge,
                  style: const TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.w700,
                    color: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: AppTheme.spacingSm),
            ],
            const Icon(
              Icons.arrow_forward_ios,
              color: Colors.white,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityInfo() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppTheme.spacingMd),
      decoration: BoxDecoration(
        color: _paymentSuccess.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _paymentSuccess.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.security,
                color: _paymentSuccess,
                size: 20,
              ),
              const SizedBox(width: AppTheme.spacingSm),
              Text(
                'Bank-Level Security',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  color: _paymentSuccess,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingSm),
          Row(
            children: [
              Icon(
                Icons.lock,
                color: _paymentSuccess,
                size: 14,
              ),
              const SizedBox(width: AppTheme.spacingXs),
              const Text(
                '256-bit SSL encryption',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingXs),
          Row(
            children: [
              Icon(
                Icons.verified_user,
                color: _paymentSuccess,
                size: 14,
              ),
              const SizedBox(width: AppTheme.spacingXs),
              const Text(
                'PCI DSS compliant',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: AppTheme.spacingXs),
          Row(
            children: [
              Icon(
                Icons.shield,
                color: _paymentSuccess,
                size: 14,
              ),
              const SizedBox(width: AppTheme.spacingXs),
              const Text(
                'Fraud protection',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    return Container(
      padding: EdgeInsets.fromLTRB(
        AppTheme.spacingMd,
        AppTheme.spacingMd,
        AppTheme.spacingMd,
        MediaQuery.of(context).padding.bottom + AppTheme.spacingMd,
      ),
      decoration: BoxDecoration(
        color: _paymentSurface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: double.infinity,
            height: 56,
            child: ElevatedButton(
              onPressed: _isProcessing ? null : _handlePayment,
              style: ElevatedButton.styleFrom(
                backgroundColor: _paymentPrimary,
                foregroundColor: Colors.white,
                elevation: 0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: _isProcessing
                  ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.lock, size: 20),
                        const SizedBox(width: AppTheme.spacingSm),
                        Text(
                          'Pay \$${widget.booking.totalAmount.toStringAsFixed(2)}',
                          style: const TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                      ],
                    ),
            ),
          ),
          const SizedBox(height: AppTheme.spacingSm),
          const Text(
            '🔒 By continuing, you agree to our Terms of Service and Privacy Policy',
            style: TextStyle(
              fontSize: 12,
              color: AppTheme.textSecondaryColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _handlePayment() async {
    setState(() => _isProcessing = true);

    try {
      // Use existing payment simulation system
      if (PaymentSimulationConfig.isSimulationEnabled) {
        PaymentSimulationConfig.logSimulation(
            'Universal payment screen initiated payment');

        // Simulate payment processing delay
        await Future.delayed(const Duration(seconds: 2));

        // Navigate using existing payment router service
        if (mounted) {
          PaymentRouterService.navigateToPayment(
            context: context,
            booking: widget.booking,
            userEmail: widget.userEmail,
            userName: widget.userName,
            userPhone: widget.userPhone,
          );
        }
      } else {
        // Production payment handling would go here
        // This integrates with the existing ProductionPaymentScreen
        if (mounted) {
          PaymentRouterService.navigateToPayment(
            context: context,
            booking: widget.booking,
            userEmail: widget.userEmail,
            userName: widget.userName,
            userPhone: widget.userPhone,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Payment failed: ${e.toString()}'),
            backgroundColor: _paymentError,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isProcessing = false);
      }
    }
  }
}
