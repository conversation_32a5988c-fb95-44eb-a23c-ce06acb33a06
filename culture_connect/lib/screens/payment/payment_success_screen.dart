import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:async';
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/models/achievement/user_achievement.dart';
import 'package:culture_connect/services/achievement_service.dart';
import 'package:culture_connect/providers/achievement_provider.dart';
import 'package:culture_connect/providers/mascot_provider.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/widgets/payment/receipt_preview_dialog.dart';
import 'package:culture_connect/screens/travel/hotel/hotel_booking_confirmation_screen.dart';
import 'package:culture_connect/screens/travel/restaurant/restaurant_booking_confirmation_screen.dart';

/// Payment success screen with Achievement and Mascot integration
/// Features celebration animations, achievement tracking, and receipt functionality
/// Performance target: <500ms screen initialization with smooth animations
class PaymentSuccessScreen extends ConsumerStatefulWidget {
  final String transactionReference;
  final double amount;
  final PaymentMethodType paymentMethod;
  final PaymentProvider provider;
  final Booking booking;
  final String? receiptId;
  final Map<String, dynamic>? bookingDetails;
  final String? userEmail;
  final String? userName;
  final String? userPhone;

  const PaymentSuccessScreen({
    super.key,
    required this.transactionReference,
    required this.amount,
    required this.paymentMethod,
    required this.provider,
    required this.booking,
    this.receiptId,
    this.bookingDetails,
    this.userEmail,
    this.userName,
    this.userPhone,
  });

  @override
  ConsumerState<PaymentSuccessScreen> createState() =>
      _PaymentSuccessScreenState();
}

class _PaymentSuccessScreenState extends ConsumerState<PaymentSuccessScreen>
    with TickerProviderStateMixin {
  late AnimationController _celebrationController;
  late AnimationController _slideController;
  late AnimationController _scaleController;
  late AnimationController _achievementController;

  // Achievement tracking
  List<UserAchievement> _unlockedAchievements = [];
  bool _showAchievementCelebration = false;

  // Screen state
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _initializeScreen();
  }

  @override
  void dispose() {
    _celebrationController.dispose();
    _slideController.dispose();
    _scaleController.dispose();
    _achievementController.dispose();
    super.dispose();
  }

  /// Initialize animation controllers
  void _initializeControllers() {
    _celebrationController = AnimationController(
      duration: AppTheme.successAnimation,
      vsync: this,
    );

    _slideController = AnimationController(
      duration: AppTheme.mediumAnimation,
      vsync: this,
    );

    _scaleController = AnimationController(
      duration: AppTheme.shortAnimation,
      vsync: this,
    );

    _achievementController = AnimationController(
      duration: AppTheme.longAnimation,
      vsync: this,
    );
  }

  /// Initialize screen with achievement tracking and mascot integration
  /// TODO: Backend Integration - Achievement Tracking API
  /// Endpoint: POST /api/achievements/track
  /// Headers: Authorization: Bearer {jwt_token}
  /// Request: { action: 'payment_completed', metadata: {...} }
  /// Response: { unlockedAchievements: [...] }
  Future<void> _initializeScreen() async {
    try {
      // Start celebration sequence
      _startCelebrationSequence();

      // Track payment completion achievement using firstBooking action
      final achievementService = ref.read(achievementServiceProvider);
      _unlockedAchievements = await achievementService.trackUserAction(
        UserAction.firstBooking,
        metadata: {
          'amount': widget.amount,
          'provider': widget.provider.name,
          'method': widget.paymentMethod.name,
          'booking_type': 'experience',
          'booking_id': widget.booking.id,
          'transaction_reference': widget.transactionReference,
        },
      );

      // Update mascot for payment success celebration
      final mascotActions = ref.read(mascotActionsProvider);
      await mascotActions.celebrateBooking(
        bookingType: 'Payment',
        bookingId: widget.transactionReference,
      );

      // Show achievement celebration if any achievements were unlocked
      if (_unlockedAchievements.isNotEmpty) {
        _showAchievementCelebration = true;

        // Trigger achievement celebration in mascot
        for (final achievement in _unlockedAchievements) {
          await mascotActions.celebrateAchievement(
            achievementId: achievement.achievementId,
            achievementTitle: achievement.achievement.title,
          );
        }

        // Start achievement animation after main celebration
        Timer(const Duration(milliseconds: 1500), () {
          if (mounted) {
            _achievementController.forward();
          }
        });
      }

      // Auto-navigate to hotel/apartment confirmation after celebration
      if (_isHotelOrApartmentBooking()) {
        debugPrint(
            'PaymentSuccessScreen: Detected hotel/apartment booking, scheduling auto-navigation');
        Timer(const Duration(milliseconds: 2500), () {
          if (mounted) {
            debugPrint(
                'PaymentSuccessScreen: Auto-navigating to hotel booking confirmation');
            _navigateToBookingConfirmation();
          }
        });
      } else if (_isRestaurantBooking()) {
        debugPrint(
            'PaymentSuccessScreen: Detected restaurant booking, scheduling auto-navigation');
        Timer(const Duration(milliseconds: 2000), () {
          if (mounted) {
            debugPrint(
                'PaymentSuccessScreen: Auto-navigating to restaurant booking confirmation');
            _navigateToBookingConfirmation();
          }
        });
      } else {
        debugPrint(
            'PaymentSuccessScreen: Not a hotel/apartment/restaurant booking, staying on success screen');
      }

      // TODO: Backend Integration - Analytics API
      // Track payment success metrics
      // ref.read(analyticsServiceProvider).logEvent(
      //   'payment_success_screen_loaded',
      //   parameters: {
      //     'transaction_reference': widget.transactionReference,
      //     'amount': widget.amount,
      //     'provider': widget.provider.name,
      //     'method': widget.paymentMethod.name,
      //     'achievements_unlocked': _unlockedAchievements.length,
      //   },
      // );

      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
      }
    } catch (e) {
      // Handle errors gracefully
      if (mounted) {
        setState(() {
          _isInitialized = true;
        });
      }
    }
  }

  /// Start celebration animation sequence with haptic feedback
  void _startCelebrationSequence() {
    // Initial success haptic feedback
    HapticFeedback.heavyImpact();

    // Start celebration animation
    _celebrationController.forward();

    // Staggered animation sequence
    Timer(const Duration(milliseconds: 300), () {
      if (mounted) {
        _slideController.forward();
        HapticFeedback.lightImpact();
      }
    });

    Timer(const Duration(milliseconds: 600), () {
      if (mounted) {
        _scaleController.forward();
        HapticFeedback.selectionClick();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: SafeArea(
        child: Stack(
          children: [
            // Background celebration effects
            _buildBackgroundCelebration(),

            // Main content
            Column(
              children: [
                // Close button
                Align(
                  alignment: Alignment.topRight,
                  child: Padding(
                    padding: const EdgeInsets.all(AppTheme.spacingMedium),
                    child: IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () => _navigateToBookingConfirmation(),
                    ),
                  ),
                ),

                // Success content
                Expanded(
                  child: _isInitialized
                      ? _buildSuccessContent(theme)
                      : _buildLoadingState(theme),
                ),

                // Action buttons
                if (_isInitialized) _buildActionButtons(theme),
              ],
            ),

            // Achievement celebration overlay
            if (_showAchievementCelebration && _unlockedAchievements.isNotEmpty)
              _buildAchievementCelebration(theme),
          ],
        ),
      ),
    );
  }

  /// Build background celebration animation
  Widget _buildBackgroundCelebration() {
    return Positioned.fill(
      child: AnimatedBuilder(
        animation: _celebrationController,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.center,
                radius: _celebrationController.value * 2.0,
                colors: [
                  Colors.green.withAlpha(51),
                  Colors.transparent,
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// Build loading state
  Widget _buildLoadingState(ThemeData theme) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: AppTheme.spacingMedium),
          Text('Processing your success...'),
        ],
      ),
    );
  }

  /// Build main success content
  Widget _buildSuccessContent(ThemeData theme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppTheme.spacingLarge),
      child: Column(
        children: [
          // Success icon with animation
          _buildSuccessIcon(theme)
              .animate(controller: _scaleController)
              .scale(begin: const Offset(0.5, 0.5), end: const Offset(1.0, 1.0))
              .fadeIn(),

          const SizedBox(height: AppTheme.spacingLarge),

          // Success message
          Text(
            'Payment Successful!',
            style: theme.textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Colors.green,
            ),
            textAlign: TextAlign.center,
          )
              .animate(controller: _slideController)
              .slideY(begin: 0.3, end: 0)
              .fadeIn(),

          const SizedBox(height: AppTheme.spacingMedium),

          // Success subtitle
          Text(
            'Your booking has been confirmed and payment processed successfully.',
            style: theme.textTheme.bodyLarge?.copyWith(
              color: theme.colorScheme.onSurface.withAlpha(179),
            ),
            textAlign: TextAlign.center,
          )
              .animate(controller: _slideController)
              .slideY(begin: 0.3, end: 0)
              .fadeIn(delay: const Duration(milliseconds: 200)),

          const SizedBox(height: AppTheme.spacingLarge),

          // Payment details card
          _buildPaymentDetailsCard(theme)
              .animate(controller: _slideController)
              .slideY(begin: 0.3, end: 0)
              .fadeIn(delay: const Duration(milliseconds: 400)),

          const SizedBox(height: AppTheme.spacingMedium),

          // Achievement notification (if any)
          if (_unlockedAchievements.isNotEmpty)
            _buildAchievementSummary(theme)
                .animate(controller: _slideController)
                .slideY(begin: 0.3, end: 0)
                .fadeIn(delay: const Duration(milliseconds: 600)),
        ],
      ),
    );
  }

  /// Build success icon with celebration animation
  Widget _buildSuccessIcon(ThemeData theme) {
    return Container(
      width: 120,
      height: 120,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: Colors.green.withAlpha(26),
        border: Border.all(
          color: Colors.green,
          width: 3,
        ),
      ),
      child: const Icon(
        Icons.check,
        size: 60,
        color: Colors.green,
      ),
    );
  }

  /// Build payment details card
  Widget _buildPaymentDetailsCard(ThemeData theme) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        side: BorderSide(
          color: theme.colorScheme.outline.withAlpha(51),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(AppTheme.spacingMedium),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Payment Details',
              style: theme.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: AppTheme.spacingMedium),
            _buildDetailRow(
                theme, 'Transaction ID', widget.transactionReference),
            const SizedBox(height: AppTheme.spacingSmall),
            _buildDetailRow(
                theme, 'Amount', '\$${widget.amount.toStringAsFixed(2)}'),
            const SizedBox(height: AppTheme.spacingSmall),
            _buildDetailRow(theme, 'Payment Method',
                _getPaymentMethodName(widget.paymentMethod)),
            const SizedBox(height: AppTheme.spacingSmall),
            _buildDetailRow(
                theme, 'Provider', widget.provider.name.toUpperCase()),
            const SizedBox(height: AppTheme.spacingSmall),
            _buildDetailRow(theme, 'Booking ID', widget.booking.id),
          ],
        ),
      ),
    );
  }

  /// Build detail row
  Widget _buildDetailRow(ThemeData theme, String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: theme.colorScheme.onSurface.withAlpha(179),
          ),
        ),
        Flexible(
          child: Text(
            value,
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.end,
          ),
        ),
      ],
    );
  }

  /// Build achievement summary
  Widget _buildAchievementSummary(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withAlpha(51),
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMedium),
        border: Border.all(
          color: theme.colorScheme.primary.withAlpha(77),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.emoji_events,
            color: theme.colorScheme.primary,
            size: 24,
          ),
          const SizedBox(width: AppTheme.spacingSmall),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${_unlockedAchievements.length} Achievement${_unlockedAchievements.length > 1 ? 's' : ''} Unlocked!',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.primary,
                  ),
                ),
                Text(
                  _unlockedAchievements.first.achievement.title,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withAlpha(179),
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            color: theme.colorScheme.primary,
            size: 16,
          ),
        ],
      ),
    );
  }

  /// Build achievement celebration overlay
  Widget _buildAchievementCelebration(ThemeData theme) {
    return Positioned.fill(
      child: AnimatedBuilder(
        animation: _achievementController,
        builder: (context, child) {
          return Container(
            color: Colors.black
                .withAlpha((128 * _achievementController.value).toInt()),
            child: Center(
              child: Transform.scale(
                scale: _achievementController.value,
                child: Card(
                  margin: const EdgeInsets.all(AppTheme.spacingLarge),
                  child: Padding(
                    padding: const EdgeInsets.all(AppTheme.spacingLarge),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          Icons.celebration,
                          size: 64,
                          color: theme.colorScheme.primary,
                        ),
                        const SizedBox(height: AppTheme.spacingMedium),
                        Text(
                          'Achievement Unlocked!',
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.primary,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: AppTheme.spacingSmall),
                        Text(
                          _unlockedAchievements.first.achievement.title,
                          style: theme.textTheme.bodyLarge,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: AppTheme.spacingMedium),
                        FilledButton(
                          onPressed: _dismissAchievementCelebration,
                          child: const Text('Continue'),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  /// Build action buttons
  Widget _buildActionButtons(ThemeData theme) {
    return Container(
      padding: const EdgeInsets.all(AppTheme.spacingMedium),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: theme.colorScheme.outline.withAlpha(51),
          ),
        ),
      ),
      child: Column(
        children: [
          // Primary action - View booking
          SizedBox(
            width: double.infinity,
            child: FilledButton(
              onPressed: _navigateToBookingConfirmation,
              child: const Text('View Booking Details'),
            ),
          ),

          const SizedBox(height: AppTheme.spacingSmall),

          // Secondary actions
          Row(
            children: [
              if (widget.receiptId != null) ...[
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: _viewReceipt,
                    icon: const Icon(Icons.receipt, size: 18),
                    label: const Text('Receipt'),
                  ),
                ),
                const SizedBox(width: AppTheme.spacingSmall),
              ],
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _shareSuccess,
                  icon: const Icon(Icons.share, size: 18),
                  label: const Text('Share'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Dismiss achievement celebration overlay
  void _dismissAchievementCelebration() {
    HapticFeedback.lightImpact();

    setState(() {
      _showAchievementCelebration = false;
    });

    _achievementController.reverse();

    // Mark achievements as celebrated
    for (final achievement in _unlockedAchievements) {
      ref.read(achievementServiceProvider).markAchievementCelebrated(
            achievement.achievementId,
          );
    }
  }

  /// Navigate to booking confirmation
  /// Intelligently routes to appropriate confirmation screen based on booking type
  void _navigateToBookingConfirmation() {
    // Check if this is a hotel/apartment booking
    if (_isHotelOrApartmentBooking()) {
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(
          builder: (context) => HotelBookingConfirmationScreen(
            booking: widget.booking,
            transactionId: widget.transactionReference,
            bookingDetails: widget.bookingDetails ?? {},
            userEmail: widget.userEmail ?? '<EMAIL>',
            userName: widget.userName ?? 'User Name',
            userPhone: widget.userPhone,
          ),
        ),
        (route) => route.isFirst,
      );
    } else if (_isRestaurantBooking()) {
      // Restaurant booking confirmation
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(
          builder: (context) => RestaurantBookingConfirmationScreen(
            booking: widget.booking,
            transactionId: widget.transactionReference,
            bookingDetails: widget.bookingDetails ?? {},
            userEmail: widget.userEmail ?? '<EMAIL>',
            userName: widget.userName ?? 'User Name',
            userPhone: widget.userPhone,
          ),
        ),
        (route) => route.isFirst,
      );
    } else {
      // Default booking confirmation for other types (experiences, flights, etc.)
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(
          builder: (context) => Scaffold(
            appBar: AppBar(title: const Text('Booking Confirmed')),
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.check_circle, color: Colors.green, size: 64),
                  const SizedBox(height: 16),
                  Text('Booking ${widget.booking.id} confirmed!'),
                  const SizedBox(height: 16),
                  const Text('Booking confirmation screen will be enhanced.'),
                ],
              ),
            ),
          ),
        ),
        (route) => route.isFirst,
      );
    }
  }

  /// Check if this is a hotel or apartment booking
  bool _isHotelOrApartmentBooking() {
    debugPrint(
        'PaymentSuccessScreen: Checking booking type - ID: ${widget.booking.id}');

    // Check booking ID prefix
    if (widget.booking.id.startsWith('hotel_') ||
        widget.booking.id.startsWith('apartment_')) {
      debugPrint(
          'PaymentSuccessScreen: Detected hotel/apartment booking by ID prefix');
      return true;
    }

    // Check booking details for hotel/apartment indicators
    if (widget.bookingDetails != null) {
      final roomType = widget.bookingDetails!['roomType'] as String? ?? '';
      final hotelName = widget.bookingDetails!['hotelName'] as String? ?? '';

      debugPrint(
          'PaymentSuccessScreen: Checking booking details - roomType: $roomType, hotelName: $hotelName');

      if (roomType.isNotEmpty || hotelName.isNotEmpty) {
        debugPrint(
            'PaymentSuccessScreen: Detected hotel/apartment booking by booking details');
        return true;
      }
    } else {
      debugPrint('PaymentSuccessScreen: No booking details available');
    }

    debugPrint('PaymentSuccessScreen: Not a hotel/apartment booking');
    return false;
  }

  /// Check if this is a restaurant booking
  bool _isRestaurantBooking() {
    debugPrint(
        'PaymentSuccessScreen: Checking restaurant booking type - ID: ${widget.booking.id}');

    // Check booking details for restaurant indicators
    if (widget.bookingDetails != null) {
      final reservation = widget.bookingDetails!['reservation'];
      final restaurant = widget.bookingDetails!['restaurant'];

      debugPrint(
          'PaymentSuccessScreen: Checking booking details - reservation: ${reservation != null}, restaurant: ${restaurant != null}');

      if (reservation != null && restaurant != null) {
        debugPrint(
            'PaymentSuccessScreen: Detected restaurant booking by booking details');
        return true;
      }
    } else {
      debugPrint(
          'PaymentSuccessScreen: No booking details available for restaurant check');
    }

    debugPrint('PaymentSuccessScreen: Not a restaurant booking');
    return false;
  }

  /// View receipt
  /// Shows a receipt preview dialog with payment details
  void _viewReceipt() {
    if (widget.receiptId == null) return;

    HapticFeedback.lightImpact();

    // Show receipt preview dialog
    showDialog(
      context: context,
      builder: (context) => ReceiptPreviewDialog(
        receiptId: widget.receiptId!,
        transactionReference: widget.transactionReference,
        amount: widget.amount,
        paymentMethod: widget.paymentMethod,
        provider: widget.provider,
        booking: widget.booking,
      ),
    );
  }

  /// Share success
  /// TODO: Backend Integration - Social Sharing
  /// Generate shareable content with booking details and success message
  /// Include deep links to app and referral codes if applicable
  void _shareSuccess() {
    HapticFeedback.lightImpact();

    // TODO: Implement sharing functionality with actual content
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: const Text('Sharing your success story...'),
        backgroundColor: Colors.green,
        action: SnackBarAction(
          label: 'Copy Link',
          onPressed: () {
            // TODO: Copy shareable link to clipboard
          },
        ),
      ),
    );
  }

  /// Get payment method display name
  String _getPaymentMethodName(PaymentMethodType method) {
    switch (method) {
      case PaymentMethodType.card:
        return 'Credit/Debit Card';
      case PaymentMethodType.bankTransfer:
        return 'Bank Transfer';
      case PaymentMethodType.ussd:
        return 'USSD';
      case PaymentMethodType.mobileMoney:
        return 'Mobile Money';
      case PaymentMethodType.crypto:
        return 'Cryptocurrency';
    }
  }
}
