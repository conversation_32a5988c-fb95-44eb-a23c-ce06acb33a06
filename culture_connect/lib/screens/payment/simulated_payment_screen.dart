// Flutter imports
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Package imports
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Project imports
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/models/car.dart';
import 'package:culture_connect/models/payment/payment_api_models.dart';
import 'package:culture_connect/screens/travel/flight/rn_flight_confirmation_screen.dart';
import 'package:culture_connect/screens/payment/payment_success_screen.dart';
import 'package:culture_connect/screens/cars/car_confirmation_screen.dart';
import 'package:culture_connect/theme/app_theme.dart';
import 'package:culture_connect/config/payment_simulation_config.dart';

/// TESTING ONLY - Simulated Payment Screen
///
/// This screen simulates the payment flow for testing the complete flight booking
/// journey without processing actual payments.
///
/// TODO: PRODUCTION DEPLOYMENT - Remove this entire file before production
/// TODO: PRODUCTION DEPLOYMENT - Restore direct ProductionPaymentScreen usage
/// TODO: PRODUCTION DEPLOYMENT - Remove all references to this simulation screen
///
/// This screen mimics the timing and navigation flow of ProductionPaymentScreen
/// to ensure the complete booking flow works correctly during testing.
class SimulatedPaymentScreen extends ConsumerStatefulWidget {
  final Booking booking;
  final String userEmail;
  final String userName;
  final String? userPhone;

  const SimulatedPaymentScreen({
    super.key,
    required this.booking,
    required this.userEmail,
    required this.userName,
    this.userPhone,
  });

  @override
  ConsumerState<SimulatedPaymentScreen> createState() =>
      _SimulatedPaymentScreenState();
}

class _SimulatedPaymentScreenState extends ConsumerState<SimulatedPaymentScreen>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _pulseController;

  PaymentSimulationState _currentState = PaymentSimulationState.initializing;
  double _progress = 0.0;
  String _statusMessage = 'Initializing payment...';

  @override
  void initState() {
    super.initState();
    PaymentSimulationConfig.logSimulation(
        'Starting payment simulation for booking: ${widget.booking.id}');

    _progressController = AnimationController(
      duration: const Duration(seconds: 5),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    )..repeat(reverse: true);

    _startPaymentSimulation();
  }

  @override
  void dispose() {
    _progressController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _startPaymentSimulation() async {
    PaymentSimulationConfig.logSimulation('Payment simulation flow started');

    // Step 1: Initialization
    setState(() {
      _currentState = PaymentSimulationState.initializing;
      _statusMessage = 'Initializing secure payment...';
    });

    await Future.delayed(Duration(
        milliseconds: PaymentSimulationConfig.PAYMENT_INITIALIZATION_DELAY));

    // Step 2: Processing
    setState(() {
      _currentState = PaymentSimulationState.processing;
      _statusMessage = 'Processing payment...';
    });

    _progressController.forward();

    // Simulate progress updates
    for (int i = 0; i <= 100; i += 10) {
      await Future.delayed(Duration(
          milliseconds:
              PaymentSimulationConfig.PAYMENT_PROCESSING_DELAY ~/ 10));
      if (mounted) {
        setState(() {
          _progress = i / 100.0;
        });
      }
    }

    // Step 3: Success
    setState(() {
      _currentState = PaymentSimulationState.success;
      _statusMessage = 'Payment successful!';
    });

    PaymentSimulationConfig.logSimulation(
        'Payment simulation completed successfully');

    await Future.delayed(
        Duration(milliseconds: PaymentSimulationConfig.PAYMENT_SUCCESS_DELAY));

    _navigateToConfirmation();
  }

  void _navigateToConfirmation() {
    PaymentSimulationConfig.logSimulation(
        'Navigating to appropriate confirmation screen');

    debugPrint(
        'SimulatedPaymentScreen: Checking booking type - ID: ${widget.booking.id}, experienceId: ${widget.booking.experienceId}');

    if (mounted) {
      // Check booking type and navigate accordingly
      if (widget.booking.experienceId.startsWith('flight_') ||
          widget.booking.specialRequirements?.contains('Seat:') == true) {
        // Flight booking - navigate to flight confirmation
        final seatId =
            widget.booking.specialRequirements?.split('Seat: ').last ?? '24C';

        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => RNFlightConfirmationScreen(
              flightId: widget.booking.experienceId,
              seatId: seatId,
            ),
          ),
        );
      } else if (widget.booking.id.startsWith('hotel_') ||
          widget.booking.id.startsWith('apartment_')) {
        // Hotel/Apartment booking - navigate to PaymentSuccessScreen with booking details
        debugPrint(
            'SimulatedPaymentScreen: Detected hotel/apartment booking, navigating to PaymentSuccessScreen');
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => PaymentSuccessScreen(
              transactionReference:
                  'sim_${DateTime.now().millisecondsSinceEpoch}',
              amount: widget.booking.totalAmount,
              paymentMethod: PaymentMethodType.card,
              provider: PaymentProvider.stripe,
              booking: widget.booking,
              bookingDetails: _extractBookingDetails(),
              userEmail: widget.userEmail,
              userName: widget.userName,
              userPhone: widget.userPhone,
            ),
          ),
        );
      } else if (widget.booking.id.startsWith('car_') ||
          widget.booking.experienceId.startsWith('car_')) {
        // Car booking - navigate to car confirmation screen
        debugPrint(
            'SimulatedPaymentScreen: Detected car booking, navigating to CarConfirmationScreen');

        // Extract car data from booking details
        final bookingDetails = _extractBookingDetails();
        final carData = {
          'id': bookingDetails['carId'] ?? 'unknown',
          'name': bookingDetails['carModel'] ?? 'Unknown Car',
          'category': bookingDetails['carType'] ?? 'Sedan',
          'pricePerDay': bookingDetails['dailyRate'] ?? 50.0,
          'location': bookingDetails['location'] ?? 'Unknown Location',
        };

        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => CarConfirmationScreen(
              car: Car(
                id: carData['id'] as String,
                name: carData['name'] as String,
                brand: 'Unknown',
                model: carData['name'] as String,
                category: carData['category'] as String,
                pricePerDay: (carData['pricePerDay'] as num).toDouble(),
                rating: 4.5,
                reviewCount: 100,
                imageUrl: '',
                features: [],
                fuelType: 'Gasoline',
                transmission: 'Automatic',
                seats: 5,
                doors: 4,
                airConditioning: true,
                available: true,
                location: carData['location'] as String,
                description: 'Car rental booking',
              ),
              bookingData: bookingDetails,
            ),
          ),
        );
      } else {
        // Default success screen for other booking types
        debugPrint(
            'SimulatedPaymentScreen: Using default success screen for booking type');
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) => Scaffold(
              appBar: AppBar(title: const Text('Payment Successful')),
              body: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.check_circle, color: Colors.green, size: 64),
                    SizedBox(height: 16),
                    Text('Payment completed successfully!'),
                    SizedBox(height: 16),
                    Text('This was a simulated payment for testing.'),
                  ],
                ),
              ),
            ),
          ),
        );
      }
    }
  }

  /// Extract booking details for hotel/apartment bookings
  Map<String, dynamic> _extractBookingDetails() {
    // For simulation, create mock booking details
    // In production, this would come from the actual booking data
    final isApartment = widget.booking.id.startsWith('apartment_');

    return {
      'hotelName':
          isApartment ? 'Luxury Downtown Apartment' : 'Grand Hotel Plaza',
      'hotelLocation': 'Downtown City Center',
      'starRating': isApartment ? 4 : 5,
      'roomType': isApartment ? 'Apartment' : 'Deluxe Room',
      'roomDescription': isApartment
          ? 'Self-catering apartment accommodation'
          : 'Comfortable hotel room',
      'checkIn': 'Dec 15, 2024',
      'checkOut': 'Dec 18, 2024',
      'nights': 3,
      'guests': widget.booking.participantCount,
      'basePrice': widget.booking.totalAmount / 3, // Assume 3 nights
      'totalPrice': widget.booking.totalAmount,
      'currency': 'USD',
      'cancellationPolicy': 'Free cancellation up to 24 hours before check-in',
      'hotelImage': 'https://example.com/hotel-image.jpg',
      'amenities': ['WiFi', 'Pool', 'Gym', 'Restaurant'],
    };
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      appBar: AppBar(
        title: const Text('Payment Processing'),
        backgroundColor: AppTheme.backgroundColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: _currentState == PaymentSimulationState.processing
              ? null
              : () => Navigator.of(context).pop(),
        ),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Simulation Mode Banner
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                margin: const EdgeInsets.only(bottom: 32),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.orange),
                ),
                child: const Column(
                  children: [
                    Icon(Icons.science, color: Colors.orange, size: 32),
                    SizedBox(height: 8),
                    Text(
                      'TESTING MODE',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                    SizedBox(height: 4),
                    Text(
                      'This is a simulated payment for testing purposes',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.orange,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              // Payment Status Icon
              _buildStatusIcon(),

              const SizedBox(height: 32),

              // Status Message
              Text(
                _statusMessage,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimaryColor,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 24),

              // Progress Indicator
              if (_currentState == PaymentSimulationState.processing)
                _buildProgressIndicator(),

              const SizedBox(height: 32),

              // Booking Details
              _buildBookingDetails(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusIcon() {
    switch (_currentState) {
      case PaymentSimulationState.initializing:
        return AnimatedBuilder(
          animation: _pulseController,
          builder: (context, child) {
            return Transform.scale(
              scale: 1.0 + (_pulseController.value * 0.1),
              child: Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.payment,
                  size: 40,
                  color: AppTheme.primaryColor,
                ),
              ),
            );
          },
        );

      case PaymentSimulationState.processing:
        return Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: const CircularProgressIndicator(
            color: AppTheme.primaryColor,
            strokeWidth: 3,
          ),
        );

      case PaymentSimulationState.success:
        return Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: Colors.green.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.check_circle,
            size: 40,
            color: Colors.green,
          ),
        );
    }
  }

  Widget _buildProgressIndicator() {
    return Column(
      children: [
        LinearProgressIndicator(
          value: _progress,
          backgroundColor: AppTheme.borderColor,
          valueColor:
              const AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
        ),
        const SizedBox(height: 8),
        Text(
          '${(_progress * 100).toInt()}%',
          style: const TextStyle(
            fontSize: 14,
            color: AppTheme.textSecondaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildBookingDetails() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.borderColor),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Booking Details',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimaryColor,
            ),
          ),
          const SizedBox(height: 12),
          _buildDetailRow('Booking ID', widget.booking.id),
          _buildDetailRow(
              'Amount', '\$${widget.booking.totalAmount.toStringAsFixed(2)}'),
          _buildDetailRow('Email', widget.userEmail),
          if (widget.booking.specialRequirements != null)
            _buildDetailRow('Details', widget.booking.specialRequirements!),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                color: AppTheme.textSecondaryColor,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: AppTheme.textPrimaryColor,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Enum to track payment simulation states
enum PaymentSimulationState {
  initializing,
  processing,
  success,
}
