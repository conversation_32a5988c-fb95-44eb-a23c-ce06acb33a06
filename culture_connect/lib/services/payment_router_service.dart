// Flutter imports
import 'package:flutter/material.dart';

// Project imports
import 'package:culture_connect/models/booking.dart';
import 'package:culture_connect/config/payment_simulation_config.dart';
import 'package:culture_connect/screens/payment/production_payment_screen.dart';
import 'package:culture_connect/screens/payment/simulated_payment_screen.dart';

/// Payment Router Service
/// 
/// This service routes payment flows between simulation and production modes
/// based on the PaymentSimulationConfig feature flag.
/// 
/// TODO: PRODUCTION DEPLOYMENT - Remove this entire service before production
/// TODO: PRODUCTION DEPLOYMENT - Replace all calls to this service with direct ProductionPaymentScreen usage
/// TODO: PRODUCTION DEPLOYMENT - Remove PaymentSimulationConfig dependency
/// 
/// Usage:
/// ```dart
/// PaymentRouterService.navigateToPayment(
///   context: context,
///   booking: booking,
///   userEmail: email,
///   userName: name,
///   userPhone: phone,
/// );
/// ```
class PaymentRouterService {
  /// Navigate to the appropriate payment screen based on simulation configuration
  /// 
  /// This method automatically routes to either:
  /// - SimulatedPaymentScreen (when ENABLE_PAYMENT_SIMULATION = true)
  /// - ProductionPaymentScreen (when ENABLE_PAYMENT_SIMULATION = false)
  /// 
  /// TODO: PRODUCTION DEPLOYMENT - Replace all calls to this method with:
  /// ```dart
  /// Navigator.of(context).push(
  ///   MaterialPageRoute(
  ///     builder: (context) => ProductionPaymentScreen(
  ///       booking: booking,
  ///       userEmail: userEmail,
  ///       userName: userName,
  ///       userPhone: userPhone,
  ///     ),
  ///   ),
  /// );
  /// ```
  static void navigateToPayment({
    required BuildContext context,
    required Booking booking,
    required String userEmail,
    required String userName,
    String? userPhone,
  }) {
    if (PaymentSimulationConfig.isSimulationEnabled) {
      PaymentSimulationConfig.logSimulation(
        'Routing to SimulatedPaymentScreen for booking: ${booking.id}'
      );
      
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => SimulatedPaymentScreen(
            booking: booking,
            userEmail: userEmail,
            userName: userName,
            userPhone: userPhone,
          ),
        ),
      );
    } else {
      PaymentSimulationConfig.logSimulation(
        'Routing to ProductionPaymentScreen for booking: ${booking.id}'
      );
      
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ProductionPaymentScreen(
            booking: booking,
            userEmail: userEmail,
            userName: userName,
            userPhone: userPhone,
          ),
        ),
      );
    }
  }

  /// Get the appropriate payment screen widget without navigation
  /// 
  /// This method returns the appropriate payment screen widget based on
  /// the simulation configuration. Useful for custom navigation scenarios.
  /// 
  /// TODO: PRODUCTION DEPLOYMENT - Replace all calls to this method with:
  /// ```dart
  /// ProductionPaymentScreen(
  ///   booking: booking,
  ///   userEmail: userEmail,
  ///   userName: userName,
  ///   userPhone: userPhone,
  /// )
  /// ```
  static Widget getPaymentScreen({
    required Booking booking,
    required String userEmail,
    required String userName,
    String? userPhone,
  }) {
    if (PaymentSimulationConfig.isSimulationEnabled) {
      PaymentSimulationConfig.logSimulation(
        'Creating SimulatedPaymentScreen widget for booking: ${booking.id}'
      );
      
      return SimulatedPaymentScreen(
        booking: booking,
        userEmail: userEmail,
        userName: userName,
        userPhone: userPhone,
      );
    } else {
      PaymentSimulationConfig.logSimulation(
        'Creating ProductionPaymentScreen widget for booking: ${booking.id}'
      );
      
      return ProductionPaymentScreen(
        booking: booking,
        userEmail: userEmail,
        userName: userName,
        userPhone: userPhone,
      );
    }
  }

  /// Check if simulation mode is currently enabled
  /// 
  /// This is a convenience method to check the simulation status
  /// without directly accessing PaymentSimulationConfig.
  /// 
  /// TODO: PRODUCTION DEPLOYMENT - Remove this method
  static bool get isSimulationMode => PaymentSimulationConfig.isSimulationEnabled;

  /// Get current payment mode as a string for debugging/logging
  /// 
  /// Returns either "SIMULATION" or "PRODUCTION" based on current configuration.
  /// 
  /// TODO: PRODUCTION DEPLOYMENT - Remove this method
  static String get currentPaymentMode => 
      PaymentSimulationConfig.isSimulationEnabled ? "SIMULATION" : "PRODUCTION";

  /// Log payment routing information
  /// 
  /// Helper method to log payment routing decisions for debugging.
  /// 
  /// TODO: PRODUCTION DEPLOYMENT - Remove this method
  static void logPaymentRouting(String bookingId, String action) {
    PaymentSimulationConfig.logSimulation(
      'Payment routing for booking $bookingId: $action (Mode: $currentPaymentMode)'
    );
  }
}
