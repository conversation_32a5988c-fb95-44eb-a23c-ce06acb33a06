import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/travel/travel.dart';
import 'package:culture_connect/models/travel/restaurant.dart';
import 'package:culture_connect/models/location/geo_location.dart';

/// Provider for a list of sample restaurants
final restaurantsProvider = Provider<List<Restaurant>>((ref) {
  return _getSampleRestaurants();
});

/// Provider for a specific restaurant by ID
final restaurantByIdProvider = Provider.family<Restaurant?, String>((ref, id) {
  final restaurants = ref.watch(restaurantsProvider);
  try {
    return restaurants.firstWhere((restaurant) => restaurant.id == id);
  } catch (e) {
    return null;
  }
});

/// Provider for featured restaurants
final featuredRestaurantsProvider = Provider<List<Restaurant>>((ref) {
  final restaurants = ref.watch(restaurantsProvider);
  return restaurants.where((restaurant) => restaurant.isFeatured).toList();
});

/// Provider for restaurants by cuisine type
final restaurantsByCuisineProvider =
    Provider.family<List<Restaurant>, CuisineType>((ref, cuisineType) {
  final restaurants = ref.watch(restaurantsProvider);
  return restaurants
      .where((restaurant) => restaurant.cuisineTypes.contains(cuisineType))
      .toList();
});

/// Get a list of sample restaurants
List<Restaurant> _getSampleRestaurants() {
  return [
    Restaurant(
      id: 'restaurant1',
      name: 'The Fancy Bistro',
      description:
          'An elegant bistro offering a fusion of French and Italian cuisine in a cozy atmosphere. Our chefs use only the freshest ingredients to create memorable dining experiences.',
      price: 150.0,
      currency: '\$',
      rating: 4.7,
      reviewCount: 128,
      imageUrl:
          'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-**********-3ed3cdb5ed0c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-1484659619207-9165d119dafe?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '123 Gourmet Street, Paris, France',
      coordinates: const GeoLocation(latitude: 48.8566, longitude: 2.3522),
      isAvailable: true,
      isFeatured: true,
      isOnSale: false,
      tags: ['Romantic', 'Fine Dining', 'Wine Selection'],
      amenities: ['Free Wi-Fi', 'Valet Parking', 'Outdoor Seating'],
      cancellationPolicy:
          'Free cancellation up to 24 hours before your reservation.',
      createdAt: DateTime.now().subtract(const Duration(days: 365)),
      updatedAt: DateTime.now().subtract(const Duration(days: 30)),
      restaurantType: RestaurantType.bistro,
      cuisineTypes: [
        CuisineType.french,
        CuisineType.italian,
        CuisineType.fusion
      ],
      menuItems: [
        const MenuItem(
          id: 'item1',
          name: 'Truffle Risotto',
          description:
              'Creamy Arborio rice with black truffle, parmesan, and wild mushrooms.',
          price: 28.0,
          currency: '\$',
          category: 'Main Course',
          isVegetarian: true,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1633964913295-ceb43826e7c7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
        const MenuItem(
          id: 'item2',
          name: 'Coq au Vin',
          description:
              'Traditional French dish of chicken braised with wine, lardons, mushrooms, and garlic.',
          price: 32.0,
          currency: '\$',
          category: 'Main Course',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: true,
          containsNuts: false,
          isSpicy: false,
          imageUrl:
              'https://images.unsplash.com/photo-1600891963935-9e2e13096d9a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
        const MenuItem(
          id: 'item3',
          name: 'Escargot de Bourgogne',
          description: 'Snails baked in a shell with parsley garlic butter.',
          price: 18.0,
          currency: '\$',
          category: 'Appetizers',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: true,
          containsNuts: false,
          isSpicy: false,
          imageUrl:
              'https://images.unsplash.com/photo-1625944525533-473f1a3d54e7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
        const MenuItem(
          id: 'item4',
          name: 'Bouillabaisse Marseillaise',
          description:
              'Traditional Provençal fish stew with saffron, fennel, and rouille sauce.',
          price: 42.0,
          currency: '\$',
          category: 'Main Course',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: true,
          containsNuts: false,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-**********-d721426d6edc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
        const MenuItem(
          id: 'item5',
          name: 'Tarte Tatin',
          description:
              'Upside-down apple tart with caramelized apples and vanilla ice cream.',
          price: 14.0,
          currency: '\$',
          category: 'Desserts',
          isVegetarian: true,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: false,
          imageUrl:
              'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
        const MenuItem(
          id: 'item6',
          name: 'French Onion Soup',
          description:
              'Classic soup with caramelized onions, beef broth, and Gruyère cheese.',
          price: 16.0,
          currency: '\$',
          category: 'Soups',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: false,
          imageUrl:
              'https://images.unsplash.com/photo-1547592166-23ac45744acd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1471&q=80',
        ),
        const MenuItem(
          id: 'item7',
          name: 'Crème Brûlée',
          description:
              'Classic French dessert featuring rich custard topped with caramelized sugar.',
          price: 12.0,
          currency: '\$',
          category: 'Desserts',
          isVegetarian: true,
          isVegan: false,
          isGlutenFree: true,
          containsNuts: false,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1470124182917-cc6e71b22ecc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
      ],
      openingHours: {
        'Monday': '5:00 PM - 10:00 PM',
        'Tuesday': '5:00 PM - 10:00 PM',
        'Wednesday': '5:00 PM - 10:00 PM',
        'Thursday': '5:00 PM - 11:00 PM',
        'Friday': '5:00 PM - 11:00 PM',
        'Saturday': '4:00 PM - 11:00 PM',
        'Sunday': '4:00 PM - 9:00 PM',
      },
      hasOutdoorSeating: true,
      hasBar: true,
      hasLiveMusic: false,
      hasKidsMenu: false,
      hasVegetarianOptions: true,
      hasVeganOptions: true,
      hasGlutenFreeOptions: true,
      hasHalalOptions: false,
      hasKosherOptions: false,
      hasDressCode: true,
      dressCodeDescription: 'Smart casual. No shorts or flip-flops.',
      requiresReservation: true,
      hasParking: true,
      hasValetParking: true,
      isWheelchairAccessible: true,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: true,
      viewDescription: 'Scenic view of the Eiffel Tower.',
    ),
    Restaurant(
      id: 'restaurant2',
      name: 'Sushi Paradise',
      description:
          'Authentic Japanese sushi restaurant offering the freshest fish and traditional preparation methods. Our master chefs have over 20 years of experience.',
      price: 120.0,
      currency: '\$',
      rating: 4.9,
      reviewCount: 256,
      imageUrl:
          'https://images.unsplash.com/photo-1579871494447-9811cf80d66c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-**********-f6e147245754?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-1617196034183-421b4917c92d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-**********-64566f976cfa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '456 Ocean Avenue, Tokyo, Japan',
      coordinates: const GeoLocation(latitude: 35.6762, longitude: 139.6503),
      isAvailable: true,
      isFeatured: true,
      isOnSale: true,
      originalPrice: 150.0,
      discountPercentage: 20,
      tags: ['Japanese', 'Sushi', 'Fresh Fish'],
      amenities: ['Sake Bar', 'Private Dining Rooms', 'Chef\'s Table'],
      cancellationPolicy:
          'Free cancellation up to 12 hours before your reservation.',
      createdAt: DateTime.now().subtract(const Duration(days: 180)),
      updatedAt: DateTime.now().subtract(const Duration(days: 15)),
      restaurantType: RestaurantType.fineDining,
      cuisineTypes: [CuisineType.japanese, CuisineType.seafood],
      menuItems: [
        const MenuItem(
          id: 'item5',
          name: 'Omakase Sushi Set',
          description:
              'Chef\'s selection of the freshest fish and seafood, served with traditional accompaniments.',
          price: 85.0,
          currency: '\$',
          category: 'Chef\'s Specials',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1579584425555-c3ce17fd4351?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
        const MenuItem(
          id: 'item6',
          name: 'Dragon Roll',
          description: 'Eel and cucumber inside, avocado and tobiko on top.',
          price: 22.0,
          currency: '\$',
          category: 'Specialty Rolls',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: false,
          imageUrl:
              'https://images.unsplash.com/photo-1617196035154-1e7e6e28b0db?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
        const MenuItem(
          id: 'item8',
          name: 'Spicy Tuna Roll',
          description: 'Fresh tuna mixed with spicy mayo and green onions.',
          price: 18.0,
          currency: '\$',
          category: 'Specialty Rolls',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: true,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1635343484272-8315e8b3e8b7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
        const MenuItem(
          id: 'item9',
          name: 'Vegetable Tempura',
          description:
              'Assorted seasonal vegetables lightly battered and fried.',
          price: 16.0,
          currency: '\$',
          category: 'Appetizers',
          isVegetarian: true,
          isVegan: true,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: false,
          imageUrl:
              'https://images.unsplash.com/photo-1615557960916-c616f8570d30?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
        const MenuItem(
          id: 'item10',
          name: 'Chirashi Bowl',
          description:
              'Assorted sashimi over seasoned sushi rice with pickled vegetables.',
          price: 32.0,
          currency: '\$',
          category: 'Rice Bowls',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-**********-f6e147245754?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
        const MenuItem(
          id: 'item11',
          name: 'Miso Soup',
          description:
              'Traditional soybean paste soup with tofu, seaweed, and green onions.',
          price: 6.0,
          currency: '\$',
          category: 'Soups',
          isVegetarian: true,
          isVegan: true,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: false,
          imageUrl:
              'https://images.unsplash.com/photo-1606491956689-2ea866880c84?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
        const MenuItem(
          id: 'item12',
          name: 'Mochi Ice Cream',
          description:
              'Sweet rice cake filled with premium ice cream in assorted flavors.',
          price: 12.0,
          currency: '\$',
          category: 'Desserts',
          isVegetarian: true,
          isVegan: false,
          isGlutenFree: true,
          containsNuts: false,
          isSpicy: false,
          imageUrl:
              'https://images.unsplash.com/photo-1563805042-7684c019e1cb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
      ],
      openingHours: {
        'Monday': 'Closed',
        'Tuesday': '12:00 PM - 2:30 PM, 5:30 PM - 10:00 PM',
        'Wednesday': '12:00 PM - 2:30 PM, 5:30 PM - 10:00 PM',
        'Thursday': '12:00 PM - 2:30 PM, 5:30 PM - 10:00 PM',
        'Friday': '12:00 PM - 2:30 PM, 5:30 PM - 11:00 PM',
        'Saturday': '5:30 PM - 11:00 PM',
        'Sunday': '5:30 PM - 9:00 PM',
      },
      hasOutdoorSeating: false,
      hasBar: true,
      hasLiveMusic: false,
      hasKidsMenu: false,
      hasVegetarianOptions: true,
      hasVeganOptions: false,
      hasGlutenFreeOptions: true,
      hasHalalOptions: false,
      hasKosherOptions: false,
      hasDressCode: false,
      dressCodeDescription: null,
      requiresReservation: true,
      hasParking: true,
      hasValetParking: false,
      isWheelchairAccessible: true,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: false,
      viewDescription: null,
    ),
    Restaurant(
      id: 'restaurant3',
      name: 'Spice Route',
      description:
          'Authentic Indian cuisine with traditional spices and cooking methods. Experience the rich flavors of India in our warm, welcoming atmosphere.',
      price: 85.0,
      currency: '\$',
      rating: 4.5,
      reviewCount: 89,
      imageUrl:
          'https://images.unsplash.com/photo-1565557623262-b51c2513a641?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1471&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-1574653853027-5d3ba0c95f5d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-1596797038530-2c107229654b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '456 Curry Lane, Mumbai, India',
      coordinates: const GeoLocation(latitude: 19.0760, longitude: 72.8777),
      isAvailable: true,
      isFeatured: false,
      isOnSale: true,
      originalPrice: 95.0,
      discountPercentage: 10,
      tags: ['Spicy', 'Vegetarian Friendly', 'Traditional'],
      amenities: ['Free Wi-Fi', 'Air Conditioning', 'Takeaway'],
      cancellationPolicy:
          'Free cancellation up to 2 hours before your reservation.',
      createdAt: DateTime.now().subtract(const Duration(days: 200)),
      updatedAt: DateTime.now().subtract(const Duration(days: 15)),
      restaurantType: RestaurantType.casual,
      cuisineTypes: [CuisineType.indian],
      menuItems: [
        const MenuItem(
          id: 'item7',
          name: 'Butter Chicken',
          description:
              'Tender chicken in a rich, creamy tomato-based sauce with aromatic spices.',
          price: 22.0,
          currency: '\$',
          category: 'Main Course',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: true,
          containsNuts: true,
          isSpicy: true,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1588166524941-3bf61a9c41db?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1484&q=80',
        ),
        const MenuItem(
          id: 'item8',
          name: 'Palak Paneer',
          description:
              'Fresh spinach curry with cubes of cottage cheese and traditional spices.',
          price: 18.0,
          currency: '\$',
          category: 'Main Course',
          isVegetarian: true,
          isVegan: false,
          isGlutenFree: true,
          containsNuts: false,
          isSpicy: true,
          imageUrl:
              'https://images.unsplash.com/photo-1631452180519-c014fe946bc7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
      ],
      openingHours: {
        'Monday': '11:00 AM - 10:00 PM',
        'Tuesday': '11:00 AM - 10:00 PM',
        'Wednesday': '11:00 AM - 10:00 PM',
        'Thursday': '11:00 AM - 10:00 PM',
        'Friday': '11:00 AM - 11:00 PM',
        'Saturday': '11:00 AM - 11:00 PM',
        'Sunday': '12:00 PM - 9:00 PM',
      },
      hasOutdoorSeating: false,
      hasBar: false,
      hasLiveMusic: false,
      hasKidsMenu: true,
      hasVegetarianOptions: true,
      hasVeganOptions: true,
      hasGlutenFreeOptions: true,
      hasHalalOptions: true,
      hasKosherOptions: false,
      hasDressCode: false,
      dressCodeDescription: null,
      requiresReservation: false,
      hasParking: true,
      hasValetParking: false,
      isWheelchairAccessible: true,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: false,
      viewDescription: null,
    ),
    Restaurant(
      id: 'restaurant4',
      name: 'La Cantina Mexicana',
      description:
          'Vibrant Mexican restaurant serving authentic tacos, burritos, and traditional dishes with fresh ingredients and bold flavors.',
      price: 65.0,
      currency: '\$',
      rating: 4.3,
      reviewCount: 156,
      imageUrl:
          'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1481&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-**********-5ee1c4a1479b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        'https://images.unsplash.com/photo-1599974579688-8dbdd335c77f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '789 Fiesta Street, Mexico City, Mexico',
      coordinates: const GeoLocation(latitude: 19.4326, longitude: -99.1332),
      isAvailable: true,
      isFeatured: true,
      isOnSale: false,
      tags: ['Casual', 'Family Friendly', 'Authentic'],
      amenities: ['Free Wi-Fi', 'Outdoor Seating', 'Live Music'],
      cancellationPolicy:
          'Free cancellation up to 1 hour before your reservation.',
      createdAt: DateTime.now().subtract(const Duration(days: 180)),
      updatedAt: DateTime.now().subtract(const Duration(days: 10)),
      restaurantType: RestaurantType.casual,
      cuisineTypes: [CuisineType.mexican],
      menuItems: [
        const MenuItem(
          id: 'item9',
          name: 'Carnitas Tacos',
          description:
              'Slow-cooked pork shoulder with onions, cilantro, and lime on corn tortillas.',
          price: 14.0,
          currency: '\$',
          category: 'Main Course',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: true,
          containsNuts: false,
          isSpicy: true,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1565299585323-38174c4a6471?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1480&q=80',
        ),
      ],
      openingHours: {
        'Monday': '11:00 AM - 9:00 PM',
        'Tuesday': '11:00 AM - 9:00 PM',
        'Wednesday': '11:00 AM - 9:00 PM',
        'Thursday': '11:00 AM - 9:00 PM',
        'Friday': '11:00 AM - 10:00 PM',
        'Saturday': '10:00 AM - 10:00 PM',
        'Sunday': '10:00 AM - 9:00 PM',
      },
      hasOutdoorSeating: true,
      hasBar: true,
      hasLiveMusic: true,
      hasKidsMenu: true,
      hasVegetarianOptions: true,
      hasVeganOptions: true,
      hasGlutenFreeOptions: true,
      hasHalalOptions: false,
      hasKosherOptions: false,
      hasDressCode: false,
      dressCodeDescription: null,
      requiresReservation: false,
      hasParking: true,
      hasValetParking: false,
      isWheelchairAccessible: true,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: false,
      viewDescription: null,
    ),
    Restaurant(
      id: 'restaurant5',
      name: 'Dragon Palace',
      description:
          'Elegant Chinese restaurant specializing in Cantonese cuisine with dim sum, Peking duck, and traditional stir-fries.',
      price: 95.0,
      currency: '\$',
      rating: 4.6,
      reviewCount: 203,
      imageUrl:
          'https://images.unsplash.com/photo-1582878826629-29b7ad1cdc43?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-1563379091339-03246963d51a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '321 Dynasty Road, Beijing, China',
      coordinates: const GeoLocation(latitude: 39.9042, longitude: 116.4074),
      isAvailable: true,
      isFeatured: false,
      isOnSale: false,
      tags: ['Traditional', 'Dim Sum', 'Family Style'],
      amenities: ['Private Dining', 'Valet Parking', 'Tea Service'],
      cancellationPolicy:
          'Free cancellation up to 4 hours before your reservation.',
      createdAt: DateTime.now().subtract(const Duration(days: 150)),
      updatedAt: DateTime.now().subtract(const Duration(days: 5)),
      restaurantType: RestaurantType.fineDining,
      cuisineTypes: [CuisineType.chinese],
      menuItems: [
        const MenuItem(
          id: 'item10',
          name: 'Peking Duck',
          description:
              'Traditional roasted duck served with pancakes, scallions, and hoisin sauce.',
          price: 45.0,
          currency: '\$',
          category: 'Main Course',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1567620832903-9fc6debc209f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1480&q=80',
        ),
      ],
      openingHours: {
        'Monday': '11:30 AM - 9:30 PM',
        'Tuesday': '11:30 AM - 9:30 PM',
        'Wednesday': '11:30 AM - 9:30 PM',
        'Thursday': '11:30 AM - 9:30 PM',
        'Friday': '11:30 AM - 10:00 PM',
        'Saturday': '11:00 AM - 10:00 PM',
        'Sunday': '11:00 AM - 9:30 PM',
      },
      hasOutdoorSeating: false,
      hasBar: false,
      hasLiveMusic: false,
      hasKidsMenu: true,
      hasVegetarianOptions: true,
      hasVeganOptions: false,
      hasGlutenFreeOptions: false,
      hasHalalOptions: false,
      hasKosherOptions: false,
      hasDressCode: true,
      dressCodeDescription: 'Smart casual attire preferred',
      requiresReservation: true,
      hasParking: true,
      hasValetParking: true,
      isWheelchairAccessible: true,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: false,
      viewDescription: null,
    ),
    Restaurant(
      id: 'restaurant6',
      name: 'Mama Mia Pizzeria',
      description:
          'Authentic Italian pizzeria with wood-fired ovens, fresh pasta, and traditional recipes passed down through generations.',
      price: 55.0,
      currency: '\$',
      rating: 4.4,
      reviewCount: 312,
      imageUrl:
          'https://images.unsplash.com/photo-1513104890138-7c749659a591?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-1571997478779-2adcbbe9ab2f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '654 Roma Street, Rome, Italy',
      coordinates: const GeoLocation(latitude: 41.9028, longitude: 12.4964),
      isAvailable: true,
      isFeatured: true,
      isOnSale: false,
      tags: ['Wood-fired', 'Family Recipe', 'Authentic'],
      amenities: ['Outdoor Seating', 'Takeaway', 'Wine Selection'],
      cancellationPolicy:
          'Free cancellation up to 30 minutes before your reservation.',
      createdAt: DateTime.now().subtract(const Duration(days: 120)),
      updatedAt: DateTime.now().subtract(const Duration(days: 3)),
      restaurantType: RestaurantType.casual,
      cuisineTypes: [CuisineType.italian],
      menuItems: [
        const MenuItem(
          id: 'item11',
          name: 'Margherita Pizza',
          description:
              'Classic pizza with San Marzano tomatoes, fresh mozzarella, and basil.',
          price: 16.0,
          currency: '\$',
          category: 'Main Course',
          isVegetarian: true,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1604382354936-07c5d9983bd3?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
      ],
      openingHours: {
        'Monday': '12:00 PM - 10:00 PM',
        'Tuesday': '12:00 PM - 10:00 PM',
        'Wednesday': '12:00 PM - 10:00 PM',
        'Thursday': '12:00 PM - 10:00 PM',
        'Friday': '12:00 PM - 11:00 PM',
        'Saturday': '12:00 PM - 11:00 PM',
        'Sunday': '12:00 PM - 10:00 PM',
      },
      hasOutdoorSeating: true,
      hasBar: true,
      hasLiveMusic: false,
      hasKidsMenu: true,
      hasVegetarianOptions: true,
      hasVeganOptions: true,
      hasGlutenFreeOptions: true,
      hasHalalOptions: false,
      hasKosherOptions: false,
      hasDressCode: false,
      dressCodeDescription: null,
      requiresReservation: false,
      hasParking: false,
      hasValetParking: false,
      isWheelchairAccessible: true,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: false,
      viewDescription: null,
    ),
    Restaurant(
      id: 'restaurant7',
      name: 'Savanna Grill',
      description:
          'Contemporary African restaurant featuring traditional dishes with modern presentation and locally sourced ingredients.',
      price: 75.0,
      currency: '\$',
      rating: 4.2,
      reviewCount: 87,
      imageUrl:
          'https://images.unsplash.com/photo-**********-d76694265947?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1469&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-**********-58d7cb561ad1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '987 Safari Avenue, Cape Town, South Africa',
      coordinates: const GeoLocation(latitude: -33.9249, longitude: 18.4241),
      isAvailable: true,
      isFeatured: false,
      isOnSale: true,
      originalPrice: 85.0,
      discountPercentage: 12,
      tags: ['Contemporary', 'Local Ingredients', 'Cultural'],
      amenities: ['Live Music', 'Outdoor Seating', 'Cultural Shows'],
      cancellationPolicy:
          'Free cancellation up to 3 hours before your reservation.',
      createdAt: DateTime.now().subtract(const Duration(days: 90)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      restaurantType: RestaurantType.casual,
      cuisineTypes: [CuisineType.african],
      menuItems: [
        const MenuItem(
          id: 'item12',
          name: 'Bobotie',
          description:
              'Traditional South African dish with spiced mince meat and egg topping.',
          price: 24.0,
          currency: '\$',
          category: 'Main Course',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: true,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-**********-d76694265947?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1469&q=80',
        ),
      ],
      openingHours: {
        'Monday': '5:00 PM - 10:00 PM',
        'Tuesday': '5:00 PM - 10:00 PM',
        'Wednesday': '5:00 PM - 10:00 PM',
        'Thursday': '5:00 PM - 10:00 PM',
        'Friday': '5:00 PM - 11:00 PM',
        'Saturday': '12:00 PM - 11:00 PM',
        'Sunday': '12:00 PM - 9:00 PM',
      },
      hasOutdoorSeating: true,
      hasBar: true,
      hasLiveMusic: true,
      hasKidsMenu: false,
      hasVegetarianOptions: true,
      hasVeganOptions: false,
      hasGlutenFreeOptions: false,
      hasHalalOptions: true,
      hasKosherOptions: false,
      hasDressCode: false,
      dressCodeDescription: null,
      requiresReservation: true,
      hasParking: true,
      hasValetParking: false,
      isWheelchairAccessible: false,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: true,
      viewDescription: 'Panoramic view of Table Mountain',
    ),
    Restaurant(
      id: 'restaurant8',
      name: 'The Steakhouse',
      description:
          'Premium steakhouse serving the finest cuts of beef with an extensive wine collection and elegant atmosphere.',
      price: 180.0,
      currency: '\$',
      rating: 4.8,
      reviewCount: 245,
      imageUrl:
          'https://images.unsplash.com/photo-**********-b9f581a1996d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-**********-450675393462?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1631&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '111 Prime Street, New York, USA',
      coordinates: const GeoLocation(latitude: 40.7128, longitude: -74.0060),
      isAvailable: true,
      isFeatured: true,
      isOnSale: false,
      tags: ['Premium', 'Wine Selection', 'Business Dining'],
      amenities: ['Valet Parking', 'Private Dining', 'Sommelier'],
      cancellationPolicy:
          'Free cancellation up to 24 hours before your reservation.',
      createdAt: DateTime.now().subtract(const Duration(days: 300)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      restaurantType: RestaurantType.fineDining,
      cuisineTypes: [CuisineType.american],
      menuItems: [
        const MenuItem(
          id: 'item13',
          name: 'Wagyu Ribeye',
          description:
              'Premium Japanese Wagyu ribeye steak grilled to perfection.',
          price: 85.0,
          currency: '\$',
          category: 'Main Course',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: true,
          containsNuts: false,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-**********-b9f581a1996d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
      ],
      openingHours: {
        'Monday': '5:00 PM - 11:00 PM',
        'Tuesday': '5:00 PM - 11:00 PM',
        'Wednesday': '5:00 PM - 11:00 PM',
        'Thursday': '5:00 PM - 11:00 PM',
        'Friday': '5:00 PM - 12:00 AM',
        'Saturday': '5:00 PM - 12:00 AM',
        'Sunday': '5:00 PM - 10:00 PM',
      },
      hasOutdoorSeating: false,
      hasBar: true,
      hasLiveMusic: false,
      hasKidsMenu: false,
      hasVegetarianOptions: true,
      hasVeganOptions: false,
      hasGlutenFreeOptions: true,
      hasHalalOptions: false,
      hasKosherOptions: false,
      hasDressCode: true,
      dressCodeDescription: 'Business casual or formal attire required',
      requiresReservation: true,
      hasParking: false,
      hasValetParking: true,
      isWheelchairAccessible: true,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: true,
      viewDescription: 'City skyline view',
    ),
    Restaurant(
      id: 'restaurant9',
      name: 'Mediterranean Breeze',
      description:
          'Fresh Mediterranean cuisine with olive oils, herbs, and seafood in a relaxed coastal atmosphere.',
      price: 90.0,
      currency: '\$',
      rating: 4.5,
      reviewCount: 167,
      imageUrl:
          'https://images.unsplash.com/photo-**********-d721426d6edc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-1540189549336-e6e99c3679fe?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '222 Coastal Drive, Santorini, Greece',
      coordinates: const GeoLocation(latitude: 36.3932, longitude: 25.4615),
      isAvailable: true,
      isFeatured: false,
      isOnSale: false,
      tags: ['Fresh Seafood', 'Healthy', 'Coastal'],
      amenities: ['Ocean View', 'Outdoor Seating', 'Fresh Catch'],
      cancellationPolicy:
          'Free cancellation up to 2 hours before your reservation.',
      createdAt: DateTime.now().subtract(const Duration(days: 75)),
      updatedAt: DateTime.now().subtract(const Duration(days: 4)),
      restaurantType: RestaurantType.casual,
      cuisineTypes: [CuisineType.mediterranean],
      menuItems: [
        const MenuItem(
          id: 'item14',
          name: 'Grilled Octopus',
          description:
              'Fresh octopus grilled with olive oil, lemon, and Mediterranean herbs.',
          price: 28.0,
          currency: '\$',
          category: 'Main Course',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: true,
          containsNuts: false,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-**********-d721426d6edc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
      ],
      openingHours: {
        'Monday': '12:00 PM - 10:00 PM',
        'Tuesday': '12:00 PM - 10:00 PM',
        'Wednesday': '12:00 PM - 10:00 PM',
        'Thursday': '12:00 PM - 10:00 PM',
        'Friday': '12:00 PM - 11:00 PM',
        'Saturday': '11:00 AM - 11:00 PM',
        'Sunday': '11:00 AM - 10:00 PM',
      },
      hasOutdoorSeating: true,
      hasBar: true,
      hasLiveMusic: false,
      hasKidsMenu: true,
      hasVegetarianOptions: true,
      hasVeganOptions: true,
      hasGlutenFreeOptions: true,
      hasHalalOptions: false,
      hasKosherOptions: false,
      hasDressCode: false,
      dressCodeDescription: null,
      requiresReservation: false,
      hasParking: true,
      hasValetParking: false,
      isWheelchairAccessible: true,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: true,
      viewDescription: 'Stunning ocean view',
    ),
    Restaurant(
      id: 'restaurant10',
      name: 'Bangkok Street Kitchen',
      description:
          'Authentic Thai street food and traditional dishes with bold flavors and fresh ingredients.',
      price: 45.0,
      currency: '\$',
      rating: 4.3,
      reviewCount: 198,
      imageUrl:
          'https://images.unsplash.com/photo-**********-0f31657def5e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-**********-a0d8f0c59eb4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '333 Spice Market, Bangkok, Thailand',
      coordinates: const GeoLocation(latitude: 13.7563, longitude: 100.5018),
      isAvailable: true,
      isFeatured: true,
      isOnSale: false,
      tags: ['Street Food', 'Spicy', 'Authentic'],
      amenities: ['Quick Service', 'Takeaway', 'Spice Level Options'],
      cancellationPolicy:
          'Free cancellation up to 30 minutes before your reservation.',
      createdAt: DateTime.now().subtract(const Duration(days: 60)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      restaurantType: RestaurantType.casual,
      cuisineTypes: [CuisineType.thai],
      menuItems: [
        const MenuItem(
          id: 'item15',
          name: 'Pad Thai',
          description:
              'Classic Thai stir-fried noodles with shrimp, tofu, bean sprouts, and tamarind sauce.',
          price: 15.0,
          currency: '\$',
          category: 'Main Course',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: true,
          containsNuts: true,
          isSpicy: true,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-**********-0f31657def5e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
      ],
      openingHours: {
        'Monday': '11:00 AM - 9:00 PM',
        'Tuesday': '11:00 AM - 9:00 PM',
        'Wednesday': '11:00 AM - 9:00 PM',
        'Thursday': '11:00 AM - 9:00 PM',
        'Friday': '11:00 AM - 10:00 PM',
        'Saturday': '11:00 AM - 10:00 PM',
        'Sunday': '11:00 AM - 9:00 PM',
      },
      hasOutdoorSeating: false,
      hasBar: false,
      hasLiveMusic: false,
      hasKidsMenu: true,
      hasVegetarianOptions: true,
      hasVeganOptions: true,
      hasGlutenFreeOptions: true,
      hasHalalOptions: true,
      hasKosherOptions: false,
      hasDressCode: false,
      dressCodeDescription: null,
      requiresReservation: false,
      hasParking: false,
      hasValetParking: false,
      isWheelchairAccessible: true,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: false,
      viewDescription: null,
    ),
    Restaurant(
      id: 'restaurant11',
      name: 'Le Petit Café',
      description:
          'Charming French café serving fresh pastries, coffee, and light meals in a cozy Parisian atmosphere.',
      price: 35.0,
      currency: '\$',
      rating: 4.1,
      reviewCount: 134,
      imageUrl:
          'https://images.unsplash.com/photo-**********-1e0d58224f24?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1447&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-**********-8be0ec4767c8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1471&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '444 Montmartre Street, Paris, France',
      coordinates: const GeoLocation(latitude: 48.8867, longitude: 2.3431),
      isAvailable: true,
      isFeatured: false,
      isOnSale: false,
      tags: ['Café', 'Pastries', 'Cozy'],
      amenities: ['Free Wi-Fi', 'Outdoor Seating', 'Fresh Pastries'],
      cancellationPolicy:
          'Free cancellation up to 15 minutes before your reservation.',
      createdAt: DateTime.now().subtract(const Duration(days: 45)),
      updatedAt: DateTime.now().subtract(const Duration(days: 2)),
      restaurantType: RestaurantType.cafe,
      cuisineTypes: [CuisineType.french],
      menuItems: [
        const MenuItem(
          id: 'item16',
          name: 'Croissant au Beurre',
          description:
              'Fresh buttery croissant baked daily with French butter.',
          price: 4.5,
          currency: '\$',
          category: 'Pastries',
          isVegetarian: true,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1555507036-ab794f4afe5e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1626&q=80',
        ),
      ],
      openingHours: {
        'Monday': '7:00 AM - 6:00 PM',
        'Tuesday': '7:00 AM - 6:00 PM',
        'Wednesday': '7:00 AM - 6:00 PM',
        'Thursday': '7:00 AM - 6:00 PM',
        'Friday': '7:00 AM - 7:00 PM',
        'Saturday': '8:00 AM - 7:00 PM',
        'Sunday': '8:00 AM - 5:00 PM',
      },
      hasOutdoorSeating: true,
      hasBar: false,
      hasLiveMusic: false,
      hasKidsMenu: false,
      hasVegetarianOptions: true,
      hasVeganOptions: false,
      hasGlutenFreeOptions: false,
      hasHalalOptions: false,
      hasKosherOptions: false,
      hasDressCode: false,
      dressCodeDescription: null,
      requiresReservation: false,
      hasParking: false,
      hasValetParking: false,
      isWheelchairAccessible: true,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: false,
      viewDescription: null,
    ),
    Restaurant(
      id: 'restaurant12',
      name: 'Seoul BBQ House',
      description:
          'Traditional Korean BBQ restaurant with premium meats, banchan, and authentic Korean flavors.',
      price: 70.0,
      currency: '\$',
      rating: 4.6,
      reviewCount: 221,
      imageUrl:
          'https://images.unsplash.com/photo-1590301157890-4810ed352733?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-**********-d76694265947?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1469&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '555 Gangnam Street, Seoul, South Korea',
      coordinates: const GeoLocation(latitude: 37.5665, longitude: 126.9780),
      isAvailable: true,
      isFeatured: false,
      isOnSale: false,
      tags: ['BBQ', 'Traditional', 'Group Dining'],
      amenities: ['Table Grills', 'Private Rooms', 'Banchan'],
      cancellationPolicy:
          'Free cancellation up to 1 hour before your reservation.',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      restaurantType: RestaurantType.casual,
      cuisineTypes: [CuisineType.korean],
      menuItems: [
        const MenuItem(
          id: 'item17',
          name: 'Bulgogi',
          description:
              'Marinated beef grilled at your table with traditional Korean sides.',
          price: 32.0,
          currency: '\$',
          category: 'Main Course',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1590301157890-4810ed352733?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
      ],
      openingHours: {
        'Monday': '5:00 PM - 11:00 PM',
        'Tuesday': '5:00 PM - 11:00 PM',
        'Wednesday': '5:00 PM - 11:00 PM',
        'Thursday': '5:00 PM - 11:00 PM',
        'Friday': '5:00 PM - 12:00 AM',
        'Saturday': '4:00 PM - 12:00 AM',
        'Sunday': '4:00 PM - 10:00 PM',
      },
      hasOutdoorSeating: false,
      hasBar: true,
      hasLiveMusic: false,
      hasKidsMenu: true,
      hasVegetarianOptions: true,
      hasVeganOptions: false,
      hasGlutenFreeOptions: false,
      hasHalalOptions: false,
      hasKosherOptions: false,
      hasDressCode: false,
      dressCodeDescription: null,
      requiresReservation: true,
      hasParking: true,
      hasValetParking: false,
      isWheelchairAccessible: true,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: false,
      viewDescription: null,
    ),
    Restaurant(
      id: 'restaurant13',
      name: 'Burger Junction',
      description:
          'Gourmet burger joint with artisanal ingredients, craft beers, and creative burger combinations.',
      price: 25.0,
      currency: '\$',
      rating: 4.0,
      reviewCount: 289,
      imageUrl:
          'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1399&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-1571091718767-18b5b1457add?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1472&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '666 Burger Lane, Austin, USA',
      coordinates: const GeoLocation(latitude: 30.2672, longitude: -97.7431),
      isAvailable: true,
      isFeatured: false,
      isOnSale: true,
      originalPrice: 30.0,
      discountPercentage: 17,
      tags: ['Casual', 'Craft Beer', 'Gourmet'],
      amenities: ['Craft Beer', 'Outdoor Seating', 'Quick Service'],
      cancellationPolicy:
          'Free cancellation up to 15 minutes before your reservation.',
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      restaurantType: RestaurantType.casual,
      cuisineTypes: [CuisineType.american],
      menuItems: [
        const MenuItem(
          id: 'item18',
          name: 'Truffle Burger',
          description:
              'Wagyu beef patty with truffle aioli, arugula, and aged cheddar.',
          price: 18.0,
          currency: '\$',
          category: 'Main Course',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1399&q=80',
        ),
      ],
      openingHours: {
        'Monday': '11:00 AM - 10:00 PM',
        'Tuesday': '11:00 AM - 10:00 PM',
        'Wednesday': '11:00 AM - 10:00 PM',
        'Thursday': '11:00 AM - 10:00 PM',
        'Friday': '11:00 AM - 11:00 PM',
        'Saturday': '11:00 AM - 11:00 PM',
        'Sunday': '11:00 AM - 9:00 PM',
      },
      hasOutdoorSeating: true,
      hasBar: true,
      hasLiveMusic: false,
      hasKidsMenu: true,
      hasVegetarianOptions: true,
      hasVeganOptions: true,
      hasGlutenFreeOptions: true,
      hasHalalOptions: false,
      hasKosherOptions: false,
      hasDressCode: false,
      dressCodeDescription: null,
      requiresReservation: false,
      hasParking: true,
      hasValetParking: false,
      isWheelchairAccessible: true,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: false,
      viewDescription: null,
    ),
    Restaurant(
      id: 'restaurant14',
      name: 'Vegan Garden',
      description:
          'Plant-based restaurant offering creative vegan dishes with organic, locally sourced ingredients.',
      price: 55.0,
      currency: '\$',
      rating: 4.4,
      reviewCount: 176,
      imageUrl:
          'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-1540420773420-3366772f4999?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1484&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '777 Green Street, Portland, USA',
      coordinates: const GeoLocation(latitude: 45.5152, longitude: -122.6784),
      isAvailable: true,
      isFeatured: true,
      isOnSale: false,
      tags: ['Vegan', 'Organic', 'Sustainable'],
      amenities: ['Organic Ingredients', 'Eco-Friendly', 'Garden View'],
      cancellationPolicy:
          'Free cancellation up to 2 hours before your reservation.',
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      restaurantType: RestaurantType.casual,
      cuisineTypes: [CuisineType.vegan],
      menuItems: [
        const MenuItem(
          id: 'item19',
          name: 'Quinoa Buddha Bowl',
          description:
              'Nutritious bowl with quinoa, roasted vegetables, avocado, and tahini dressing.',
          price: 16.0,
          currency: '\$',
          category: 'Main Course',
          isVegetarian: true,
          isVegan: true,
          isGlutenFree: true,
          containsNuts: true,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1512621776951-a57141f2eefd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
      ],
      openingHours: {
        'Monday': '10:00 AM - 8:00 PM',
        'Tuesday': '10:00 AM - 8:00 PM',
        'Wednesday': '10:00 AM - 8:00 PM',
        'Thursday': '10:00 AM - 8:00 PM',
        'Friday': '10:00 AM - 9:00 PM',
        'Saturday': '9:00 AM - 9:00 PM',
        'Sunday': '9:00 AM - 8:00 PM',
      },
      hasOutdoorSeating: true,
      hasBar: false,
      hasLiveMusic: false,
      hasKidsMenu: true,
      hasVegetarianOptions: true,
      hasVeganOptions: true,
      hasGlutenFreeOptions: true,
      hasHalalOptions: false,
      hasKosherOptions: false,
      hasDressCode: false,
      dressCodeDescription: null,
      requiresReservation: false,
      hasParking: true,
      hasValetParking: false,
      isWheelchairAccessible: true,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: true,
      viewDescription: 'Garden and city view',
    ),
    Restaurant(
      id: 'restaurant15',
      name: 'Rooftop Lounge',
      description:
          'Upscale rooftop restaurant with panoramic city views, craft cocktails, and modern fusion cuisine.',
      price: 140.0,
      currency: '\$',
      rating: 4.7,
      reviewCount: 198,
      imageUrl:
          'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-1517248135467-4c7edcad34c4?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '888 Skyline Tower, Dubai, UAE',
      coordinates: const GeoLocation(latitude: 25.2048, longitude: 55.2708),
      isAvailable: true,
      isFeatured: true,
      isOnSale: false,
      tags: ['Rooftop', 'City Views', 'Cocktails'],
      amenities: ['Panoramic Views', 'Craft Cocktails', 'Valet Parking'],
      cancellationPolicy:
          'Free cancellation up to 24 hours before your reservation.',
      createdAt: DateTime.now().subtract(const Duration(days: 10)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      restaurantType: RestaurantType.fineDining,
      cuisineTypes: [CuisineType.fusion],
      menuItems: [
        const MenuItem(
          id: 'item20',
          name: 'Seared Tuna Tower',
          description:
              'Fresh tuna tower with avocado, mango, and citrus vinaigrette.',
          price: 38.0,
          currency: '\$',
          category: 'Appetizers',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: true,
          containsNuts: false,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
      ],
      openingHours: {
        'Monday': '6:00 PM - 1:00 AM',
        'Tuesday': '6:00 PM - 1:00 AM',
        'Wednesday': '6:00 PM - 1:00 AM',
        'Thursday': '6:00 PM - 2:00 AM',
        'Friday': '6:00 PM - 2:00 AM',
        'Saturday': '6:00 PM - 2:00 AM',
        'Sunday': '6:00 PM - 12:00 AM',
      },
      hasOutdoorSeating: true,
      hasBar: true,
      hasLiveMusic: true,
      hasKidsMenu: false,
      hasVegetarianOptions: true,
      hasVeganOptions: false,
      hasGlutenFreeOptions: true,
      hasHalalOptions: true,
      hasKosherOptions: false,
      hasDressCode: true,
      dressCodeDescription: 'Smart casual to formal attire required',
      requiresReservation: true,
      hasParking: false,
      hasValetParking: true,
      isWheelchairAccessible: true,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: true,
      viewDescription: 'Panoramic city and ocean views',
    ),

    // Additional restaurants for better tier distribution

    // High-End Restaurants
    Restaurant(
      id: 'restaurant13',
      name: 'Le Château Noir',
      description:
          'Michelin-starred French restaurant offering an exquisite fine dining experience with seasonal tasting menus and an extensive wine cellar.',
      price: 280.0,
      currency: '\$',
      rating: 4.9,
      reviewCount: 89,
      imageUrl:
          'https://images.unsplash.com/photo-**********-94e220e084d2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-**********-11d035aa65de?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '1 Michelin Way, Lyon, France',
      coordinates: const GeoLocation(latitude: 45.7640, longitude: 4.8357),
      isAvailable: true,
      isFeatured: true,
      isOnSale: false,
      tags: ['Michelin Star', 'Tasting Menu', 'Wine Pairing'],
      amenities: ['Sommelier', 'Private Dining', 'Valet Parking'],
      cancellationPolicy:
          'Strict cancellation policy - 48 hours notice required.',
      createdAt: DateTime.now().subtract(const Duration(days: 400)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      restaurantType: RestaurantType.fineDining,
      cuisineTypes: [CuisineType.french, CuisineType.european],
      menuItems: [
        const MenuItem(
          id: 'item21',
          name: 'Foie Gras Terrine',
          description:
              'Traditional French foie gras with brioche and fig compote.',
          price: 45.0,
          currency: '\$',
          category: 'Appetizers',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
      ],
      openingHours: {
        'Monday': 'Closed',
        'Tuesday': '7:00 PM - 11:00 PM',
        'Wednesday': '7:00 PM - 11:00 PM',
        'Thursday': '7:00 PM - 11:00 PM',
        'Friday': '7:00 PM - 11:30 PM',
        'Saturday': '7:00 PM - 11:30 PM',
        'Sunday': '7:00 PM - 10:00 PM',
      },
      hasOutdoorSeating: false,
      hasBar: true,
      hasLiveMusic: false,
      hasKidsMenu: false,
      hasVegetarianOptions: true,
      hasVeganOptions: false,
      hasGlutenFreeOptions: true,
      hasHalalOptions: false,
      hasKosherOptions: false,
      hasDressCode: true,
      dressCodeDescription: 'Smart casual to formal attire required',
      requiresReservation: true,
      hasParking: true,
      hasValetParking: true,
      isWheelchairAccessible: true,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: false,
    ),

    // High-End Lounge
    Restaurant(
      id: 'restaurant14',
      name: 'The Diamond Lounge',
      description:
          'Exclusive rooftop lounge with premium cocktails, caviar service, and live jazz performances in an intimate setting.',
      price: 200.0,
      currency: '\$',
      rating: 4.8,
      reviewCount: 156,
      imageUrl:
          'https://images.unsplash.com/photo-1514933651103-005eec06c04b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-1470337458703-46ad1756a187?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '88 Penthouse Plaza, Manhattan, USA',
      coordinates: const GeoLocation(latitude: 40.7589, longitude: -73.9851),
      isAvailable: true,
      isFeatured: true,
      isOnSale: false,
      tags: ['Exclusive', 'Jazz', 'Premium Cocktails'],
      amenities: ['Live Jazz', 'Caviar Service', 'Premium Bar'],
      cancellationPolicy:
          'Strict cancellation policy - 48 hours notice required.',
      createdAt: DateTime.now().subtract(const Duration(days: 250)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      restaurantType: RestaurantType.lounge,
      cuisineTypes: [CuisineType.international, CuisineType.fusion],
      menuItems: [
        const MenuItem(
          id: 'item22',
          name: 'Beluga Caviar Service',
          description: 'Premium Beluga caviar with traditional accompaniments.',
          price: 120.0,
          currency: '\$',
          category: 'Appetizers',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: true,
          containsNuts: false,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-**********-d76694265947?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
      ],
      openingHours: {
        'Monday': 'Closed',
        'Tuesday': 'Closed',
        'Wednesday': '8:00 PM - 2:00 AM',
        'Thursday': '8:00 PM - 2:00 AM',
        'Friday': '8:00 PM - 3:00 AM',
        'Saturday': '8:00 PM - 3:00 AM',
        'Sunday': '8:00 PM - 1:00 AM',
      },
      hasOutdoorSeating: true,
      hasBar: true,
      hasLiveMusic: true,
      hasKidsMenu: false,
      hasVegetarianOptions: true,
      hasVeganOptions: false,
      hasGlutenFreeOptions: true,
      hasHalalOptions: false,
      hasKosherOptions: false,
      hasDressCode: true,
      dressCodeDescription: 'Upscale dress code - no casual wear',
      requiresReservation: true,
      hasParking: false,
      hasValetParking: true,
      isWheelchairAccessible: false,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: true,
      viewDescription: 'Manhattan skyline and Central Park views',
    ),

    // Low-End Restaurants
    Restaurant(
      id: 'restaurant15',
      name: 'Quick Bites Express',
      description:
          'Fast and affordable dining with fresh sandwiches, salads, and quick meals perfect for busy travelers.',
      price: 15.0,
      currency: '\$',
      rating: 4.2,
      reviewCount: 342,
      imageUrl:
          'https://images.unsplash.com/photo-1571091718767-18b5b1457add?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '123 Fast Lane, Downtown, USA',
      coordinates: const GeoLocation(latitude: 40.7128, longitude: -74.0060),
      isAvailable: true,
      isFeatured: false,
      isOnSale: true,
      originalPrice: 18.0,
      discountPercentage: 17,
      tags: ['Quick Service', 'Affordable', 'Fresh'],
      amenities: ['Free Wi-Fi', 'Quick Service', 'Takeaway'],
      cancellationPolicy:
          'Flexible cancellation - cancel up to 30 minutes before.',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      restaurantType: RestaurantType.fastFood,
      cuisineTypes: [CuisineType.american, CuisineType.international],
      menuItems: [
        const MenuItem(
          id: 'item23',
          name: 'Club Sandwich',
          description:
              'Triple-decker sandwich with turkey, bacon, lettuce, and tomato.',
          price: 8.5,
          currency: '\$',
          category: 'Main Course',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1553909489-cd47e0ef937f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
      ],
      openingHours: {
        'Monday': '7:00 AM - 9:00 PM',
        'Tuesday': '7:00 AM - 9:00 PM',
        'Wednesday': '7:00 AM - 9:00 PM',
        'Thursday': '7:00 AM - 9:00 PM',
        'Friday': '7:00 AM - 10:00 PM',
        'Saturday': '8:00 AM - 10:00 PM',
        'Sunday': '8:00 AM - 8:00 PM',
      },
      hasOutdoorSeating: false,
      hasBar: false,
      hasLiveMusic: false,
      hasKidsMenu: true,
      hasVegetarianOptions: true,
      hasVeganOptions: true,
      hasGlutenFreeOptions: true,
      hasHalalOptions: false,
      hasKosherOptions: false,
      hasDressCode: false,
      requiresReservation: false,
      hasParking: false,
      hasValetParking: false,
      isWheelchairAccessible: true,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: false,
    ),

    Restaurant(
      id: 'restaurant16',
      name: 'Corner Coffee & Bagels',
      description:
          'Cozy neighborhood café serving fresh coffee, bagels, and light breakfast items with a friendly atmosphere.',
      price: 12.0,
      currency: '\$',
      rating: 4.4,
      reviewCount: 287,
      imageUrl:
          'https://images.unsplash.com/photo-1501339847302-ac426a4a7cbb?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-1495474472287-4d71bcdd2085?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '456 Corner Street, Brooklyn, USA',
      coordinates: const GeoLocation(latitude: 40.6782, longitude: -73.9442),
      isAvailable: true,
      isFeatured: false,
      isOnSale: false,
      tags: ['Coffee', 'Bagels', 'Breakfast'],
      amenities: ['Free Wi-Fi', 'Fresh Coffee', 'Outdoor Seating'],
      cancellationPolicy:
          'Flexible cancellation - cancel up to 15 minutes before.',
      createdAt: DateTime.now().subtract(const Duration(days: 25)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      restaurantType: RestaurantType.cafe,
      cuisineTypes: [CuisineType.american],
      menuItems: [
        const MenuItem(
          id: 'item24',
          name: 'Everything Bagel with Cream Cheese',
          description: 'Fresh everything bagel with house-made cream cheese.',
          price: 4.5,
          currency: '\$',
          category: 'Breakfast',
          isVegetarian: true,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: false,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1509440159596-0249088772ff?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
      ],
      openingHours: {
        'Monday': '6:00 AM - 3:00 PM',
        'Tuesday': '6:00 AM - 3:00 PM',
        'Wednesday': '6:00 AM - 3:00 PM',
        'Thursday': '6:00 AM - 3:00 PM',
        'Friday': '6:00 AM - 4:00 PM',
        'Saturday': '7:00 AM - 4:00 PM',
        'Sunday': '7:00 AM - 3:00 PM',
      },
      hasOutdoorSeating: true,
      hasBar: false,
      hasLiveMusic: false,
      hasKidsMenu: true,
      hasVegetarianOptions: true,
      hasVeganOptions: true,
      hasGlutenFreeOptions: true,
      hasHalalOptions: false,
      hasKosherOptions: false,
      hasDressCode: false,
      requiresReservation: false,
      hasParking: false,
      hasValetParking: false,
      isWheelchairAccessible: true,
      hasWifi: true,
      acceptsCreditCards: true,
      hasView: false,
    ),

    Restaurant(
      id: 'restaurant17',
      name: 'Street Food Paradise',
      description:
          'Authentic street food experience with diverse international flavors, quick service, and budget-friendly prices.',
      price: 8.0,
      currency: '\$',
      rating: 4.3,
      reviewCount: 456,
      imageUrl:
          'https://images.unsplash.com/photo-**********-58d7cb561ad1?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      additionalImages: [
        'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
      ],
      provider: 'CultureConnect Dining',
      location: '789 Food Truck Plaza, Austin, USA',
      coordinates: const GeoLocation(latitude: 30.2672, longitude: -97.7431),
      isAvailable: true,
      isFeatured: false,
      isOnSale: true,
      originalPrice: 10.0,
      discountPercentage: 20,
      tags: ['Street Food', 'International', 'Budget-Friendly'],
      amenities: ['Quick Service', 'Outdoor Seating', 'Diverse Menu'],
      cancellationPolicy: 'Super flexible - cancel up to 15 minutes before.',
      createdAt: DateTime.now().subtract(const Duration(days: 20)),
      updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      restaurantType: RestaurantType.streetFood,
      cuisineTypes: [
        CuisineType.international,
        CuisineType.asian,
        CuisineType.mexican
      ],
      menuItems: [
        const MenuItem(
          id: 'item25',
          name: 'Korean BBQ Tacos',
          description:
              'Fusion tacos with Korean BBQ beef, kimchi, and spicy mayo.',
          price: 6.0,
          currency: '\$',
          category: 'Main Course',
          isVegetarian: false,
          isVegan: false,
          isGlutenFree: false,
          containsNuts: false,
          isSpicy: true,
          isPopular: true,
          imageUrl:
              'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1470&q=80',
        ),
      ],
      openingHours: {
        'Monday': '11:00 AM - 9:00 PM',
        'Tuesday': '11:00 AM - 9:00 PM',
        'Wednesday': '11:00 AM - 9:00 PM',
        'Thursday': '11:00 AM - 10:00 PM',
        'Friday': '11:00 AM - 11:00 PM',
        'Saturday': '11:00 AM - 11:00 PM',
        'Sunday': '12:00 PM - 8:00 PM',
      },
      hasOutdoorSeating: true,
      hasBar: false,
      hasLiveMusic: false,
      hasKidsMenu: true,
      hasVegetarianOptions: true,
      hasVeganOptions: true,
      hasGlutenFreeOptions: false,
      hasHalalOptions: true,
      hasKosherOptions: false,
      hasDressCode: false,
      requiresReservation: false,
      hasParking: true,
      hasValetParking: false,
      isWheelchairAccessible: true,
      hasWifi: false,
      acceptsCreditCards: true,
      hasView: false,
    ),
  ];
}
